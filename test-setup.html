<!DOCTYPE html>
<html>
<head>
    <title>Setup Test Analytics Data</title>
</head>
<body>
    <h1>Setup Test Analytics Data</h1>
    <button onclick="setupTestData()">Setup Test Analytics Data</button>
    <button onclick="clearData()">Clear All Data</button>
    <div id="output"></div>
    
    <script>
        function setupTestData() {
            const testData = {
                accounts_engaged: [
                    { total_value: 1250, date: "2024-01-01" },
                    { total_value: 1380, date: "2024-01-02" },
                    { total_value: 1420, date: "2024-01-03" }
                ],
                reach: [
                    { total_value: 5200, date: "2024-01-01" },
                    { total_value: 5800, date: "2024-01-02" },
                    { total_value: 6100, date: "2024-01-03" }
                ],
                likes: [
                    { total_value: 320, date: "2024-01-01" },
                    { total_value: 380, date: "2024-01-02" },
                    { total_value: 420, date: "2024-01-03" }
                ],
                comments: [
                    { total_value: 45, date: "2024-01-01" },
                    { total_value: 52, date: "2024-01-02" },
                    { total_value: 61, date: "2024-01-03" }
                ],
                follower_count: [
                    { total_value: 12543, date: "2024-01-01" },
                    { total_value: 12580, date: "2024-01-02" },
                    { total_value: 12620, date: "2024-01-03" }
                ],
                follower_demographics: [
                    { breakdown: { gender: "male" }, total_value: 7800, percentage: 62.5 },
                    { breakdown: { gender: "female" }, total_value: 4720, percentage: 37.5 }
                ],
                views: [
                    { total_value: 8500, date: "2024-01-01" },
                    { total_value: 9200, date: "2024-01-02" },
                    { total_value: 9800, date: "2024-01-03" }
                ],
                total_interactions: [
                    { total_value: 365, date: "2024-01-01" },
                    { total_value: 432, date: "2024-01-02" },
                    { total_value: 481, date: "2024-01-03" }
                ],
                follows_and_unfollows: [],
                engaged_audience_demographics: [],
                profile_links_taps: [],
                replies: [],
                saves: [],
                shares: [],
                followers_count: 12620,
                media_count: 145
            };
            
            // Set up the generic analytics_data key
            localStorage.setItem('analytics_data', JSON.stringify(testData));
            
            document.getElementById('output').innerHTML = 
                '<p>✅ Test analytics data has been set up!</p>' +
                '<p>Key created: analytics_data</p>' +
                '<p>Data includes: reach (' + testData.reach.length + ' items), accounts_engaged (' + testData.accounts_engaged.length + ' items), etc.</p>' +
                '<p><a href="http://localhost:3001/dashboard" target="_blank">Open Dashboard</a></p>';
        }
        
        function clearData() {
            const keys = Object.keys(localStorage);
            keys.forEach(key => localStorage.removeItem(key));
            document.getElementById('output').innerHTML = '<p>✅ All localStorage data cleared</p>';
        }
    </script>
</body>
</html>
