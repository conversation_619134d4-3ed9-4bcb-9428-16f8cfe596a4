import tailwindcssAnimate from "tailwindcss-animate";

const config = {
  content: [
    "./src/**/*.{js,ts,jsx,tsx}",
    "./components/**/*.{js,ts,jsx,tsx}",
    "./app/**/*.{js,ts,jsx,tsx}",
    "./pages/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ["var(--font-inter)", "var(--font-geist-sans)", "system-ui", "sans-serif"],
      },
      keyframes: {
        "loading-slide": {
          "0%": {
            transform: "translateX(-100%)",
          },
          "100%": {
            transform: "translateX(100%)",
          },
        },
        loading: {
          "0%": { transform: "translateX(-100%)" },
          "100%": { transform: "translateX(100%)" },
        },
        bounceUp: {
          "0%": {
            transform: "translateY(30px)",
          },
          "50%": {
            transform: "translateY(-20px)",
          },
          "100%": {
            transform: "translateY(0)",
          },
        },
        typing: {
          "0%": {
            width: "0",
          },
          "100%": {
            width: "100%",
          },
        },
        "accordion-down": {
          from: {
            height: "0",
          },
          to: {
            height: "var(--radix-accordion-content-height)",
          },
        },
        "accordion-up": {
          from: {
            height: "var(--radix-accordion-content-height)",
          },
          to: {
            height: "0",
          },
        },
        shimmer: {
          "100%": {
            transform: "translateX(100%)",
          },
        },
        "toast-enter": {
          "0%": {
            transform: "translateX(100%) scale(0.9)",
            opacity: "0",
          },
          "100%": {
            transform: "translateX(0) scale(1)",
            opacity: "1",
          },
        },
        "toast-leave": {
          "0%": {
            transform: "translateX(0) scale(1)",
            opacity: "1",
          },
          "100%": {
            transform: "translateX(100%) scale(0.9)",
            opacity: "0",
          },
        },
        "toast-progress": {
          "0%": { width: "100%" },
          "100%": { width: "0%" },
        },
        softGlow: {
          "0%": {
            boxShadow: "0 0 0px rgba(178,191,165,0.00)",
          },
          "100%": {
            boxShadow: "0 0 18px rgba(178,191,165,0.28)",
          },
        },
        gradientX: {
          "0%": {
            backgroundPosition: "0% 50%",
          },
          "100%": {
            backgroundPosition: "100% 50%",
          },
        },
      },
      colors: {
        primary: {
          light: "#e6f0ff",
          "light-hover": "#cce0ff",
          "light-active": "#99c2ff",
          normal: "#0065ff",
          "normal-hover": "#0057db",
          "normal-active": "#004fc4",
          dark: "#0040a0",
          "dark-hover": "#003380",
          "dark-active": "#002966",
          darker: "#001f4d",
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        // Dark blue colors from the design system
        "dark-blue": {
          light: "#dbeafe",
          "light-hover": "#bfdbfe",
          "light-active": "#93c5fd",
          normal: "#1e3a8a",
          "normal-hover": "#1e40af",
          "normal-active": "#1d4ed8",
          dark: "#1e3a8a",
          "dark-hover": "#1e40af",
          "dark-active": "#1d4ed8",
          darker: "#0f172a",
        },
        secondary: {
          light: "#f2f4f1",
          "light-hover": "#e5e8e3",
          "light-active": "#d7dcd5",
          normal: "#b2cba1",
          "normal-hover": "#b3bead",
          "normal-active": "#a4b29e",
          dark: "#95a690",
          "dark-hover": "#869a81",
          "dark-active": "#778e73",
          darker: "#687f64",
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        secondary2: {
          light: "#f2f2f4",
          "light-hover": "#e5e5e8",
          "light-active": "#d7d7dc",
          normal: "#b3b3bb",
          "normal-hover": "#9999a3",
          "normal-active": "#80808c",
          dark: "#666675",
          "dark-hover": "#4d4d5e",
          "dark-active": "#333347",
          darker: "#1a1a30",
        },
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        chart: {
          1: "hsl(var(--chart-1))",
          2: "hsl(var(--chart-2))",
          3: "hsl(var(--chart-3))",
          4: "hsl(var(--chart-4))",
          5: "hsl(var(--chart-5))",
        },
      },
      animation: {
        "loading-slide": "loading-slide 1s ease-in-out infinite",
        loading: "loading 1.5s ease-in-out infinite",
        bounceUp: "bounceUp 1s ease-in-out",
        typing: "typing 2s steps(8,end) infinite",
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        scroll: "scroll 20s linear infinite",
        "scroll-20s-linear-infinite": "scroll 20s linear infinite",
        scale: "scale 2s ease-in-out infinite",
        pulse: "pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite",
        shimmer: "shimmer 1.5s infinite",
        "toast-enter":
          "toast-enter 0.3s cubic-bezier(0.16, 1, 0.3, 1) forwards",
        "toast-leave": "toast-leave 0.2s cubic-bezier(0.4, 0, 1, 1) forwards",
        "toast-progress": "toast-progress linear forwards",
        "soft-glow": "softGlow 2.4s ease-in-out infinite alternate",
        "gradient-x": "gradientX 6s ease-in-out infinite alternate",
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
    },
  },
  plugins: [tailwindcssAnimate],
};

export default config;
