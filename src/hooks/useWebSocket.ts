"use client";
import { useState, useEffect, useCallback, useRef, useMemo } from "react";
import { create } from "zustand";
import { useUserStore } from "../store/userStore";
import axios from "axios";

interface WebSocketState {
  socket: WebSocket | null;

  webSocketData: any[];
  setSocket: (socket: WebSocket | null) => void;

  setWebSocketData: (data: any) => void;
  connectionStatus: string;
  setConnectionStatus: (status: string) => void;
}

// Add this helper function near the top of the file
const logSocketMessage = (prefix: string, data: any) => {
  console.log(`${prefix} Socket Message:`, {
    timestamp: new Date().toISOString(),
    data: data,
  });
};
// Normalize a URL or path by prefixing with NEXT_PUBLIC_API_URL only when needed
const normalizeUrl = (path?: string | null): string => {
  if (!path) return "";
  // Already absolute or data URL
  if (/^https?:\/\//i.test(path) || path.startsWith("data:")) {
    return path;
  }
  const base = process.env.NEXT_PUBLIC_API_URL || "";
  if (!base) return path; // Fallback: return as-is if no base is set
  const needsSlash = !base.endsWith("/") && !path.startsWith("/");
  const dedupSlash =
    base.endsWith("/") && path.startsWith("/") ? path.slice(1) : path;
  return needsSlash ? `${base}/${path}` : `${base}${dedupSlash}`;
};

// Update your store creation
const useWebSocketStore = create<WebSocketState>((set) => ({
  socket: null,

  webSocketData: [],
  setSocket: (socket) => set({ socket }),
  setWebSocketData: (data) =>
    set((state) => {
      const newData = [...state.webSocketData, data];
      return {
        webSocketData: newData.length > 100 ? newData.slice(-100) : newData,
      };
    }),
  connectionStatus: "disconnected",
  setConnectionStatus: (status) => set({ connectionStatus: status }),
}));

export function useWebSocket() {
  const {
    workspacesDetails,
    setUser,
    workspaces,
    isLoading,
    imageLoading,
    selectedWorkspace,
    selectedWorkspaceDetails,
    workspaceLogo,
    profilePhoto,
    tokenRate,
    csrfToken,
  } = useUserStore();
  const {
    socket,
    webSocketData,
    setSocket,
    setWebSocketData,
    setConnectionStatus,
  } = useWebSocketStore();
  const [videoId, setVideoId] = useState<string | null>(null);
  const [imageId, setImageId] = useState<string | null>(null);
  const [session, setSession] = useState<{
    user?: { accessToken?: string };
    status?: string;
  } | null>(null);
  const [access, setAccess] = useState<string | null>("");
  const [reconnectAttempt, setReconnectAttempt] = useState(0);
  const messageHandlersRef = useRef(new Map());
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    const status = sessionStorage.getItem("status");
    const accessToken = sessionStorage.getItem("accessToken");
    setAccess(accessToken);

    if (status && accessToken) {
      setSession({
        user: { accessToken },
        status,
      });
    }
  }, []);

  const initializeWebSocket = useMemo(() => {
    setUser({ initialLoad: true });
    return () => {
      return new Promise((resolve, reject) => {
        let retryCount = 0;
        const maxRetries = 3;
        const retryDelay = 1000; // 1 second delay between retries

        const attemptConnection = () => {
          // If there's already an active connection, resolve with it
          if (socket?.readyState === WebSocket.OPEN) {
            console.log("WebSocket already connected");
            resolve(socket);
            return;
          }

          if (session?.user?.accessToken && !socket) {
            setAccess(session.user.accessToken);

            if (!socket || socket.readyState === WebSocket.CLOSED) {
              const ws = new WebSocket(
                `wss://businessinsight.ai/ws/user/?access_token=${encodeURIComponent(
                  session.user.accessToken
                )}`
              );

              ws.onopen = () => {
                console.log("WebSocket connected");
                setSocket(ws);
                setConnectionStatus("connected");
                setReconnectAttempt(0);
                if (reconnectAttempt > 0) {
                  getProfile(true);
                } else {
                  getProfile(false);
                }
                resolve(ws);
              };

              ws.onmessage = (event) => {
                console.log(event);
                setWebSocketData(event.data);
                const data = JSON.parse(event.data);
                logSocketMessage("Main", data);

                // Handle each message in a separate async function to prevent race conditions
                const handleMessage = async () => {
                  if (data.job === "update_workspace_members") {
                    console.log("update_workspace_members response: ", data);

                    // Validate data exists and has expected structure
                    if (!data || typeof data !== "object") {
                      console.error(
                        "Invalid data received for workspace members update"
                      );
                      return;
                    }

                    // Only proceed if workspacesDetails exists
                    if (
                      !workspacesDetails ||
                      !Array.isArray(workspacesDetails)
                    ) {
                      console.error("No workspace details available to update");
                      return;
                    }

                    // Create a deep copy to prevent mutation during async operation
                    const workspaceDetailsCopy = JSON.parse(
                      JSON.stringify(workspacesDetails)
                    );

                    const updatedWorkspacesDetails = workspaceDetailsCopy.map(
                      (workspace) => {
                        if (
                          !workspace ||
                          typeof workspace !== "object" ||
                          !("workspace_name" in workspace)
                        ) {
                          return workspace;
                        }

                        const workspaceMembers = data[workspace.workspace_name];
                        if (
                          workspaceMembers &&
                          Array.isArray(workspaceMembers)
                        ) {
                          setUser({ newMemberAdded: true });
                          return {
                            ...workspace,
                            members: workspaceMembers,
                          };
                        }
                        return workspace;
                      }
                    );

                    const selectedWorkspaceDetail =
                      updatedWorkspacesDetails?.find(
                        (workspace) =>
                          workspace &&
                          typeof workspace === "object" &&
                          "workspace_name" in workspace &&
                          workspace.workspace_name === selectedWorkspace
                      );

                    if (selectedWorkspaceDetail) {
                      // Use a single setUser call to update all related state
                      setUser({
                        workspacesDetails: updatedWorkspacesDetails,
                        selectedWorkspaceDetails: selectedWorkspaceDetail,
                        selectedWorkspace:
                          selectedWorkspaceDetail.workspace_name,
                      });
                    }
                  }
                };

                // Execute the handler and catch any errors
                handleMessage().catch((error) => {
                  console.error("Error handling WebSocket message:", error);
                });
              };

              ws.onclose = (event) => {
                console.log(
                  `WebSocket disconnected with code: ${event.code}. Reason: ${
                    event.reason || "No reason provided"
                  }`
                );
                setSocket(null);
                setConnectionStatus("disconnected");

                if (retryCount < maxRetries) {
                  console.log(
                    `Connection attempt ${
                      retryCount + 1
                    } of ${maxRetries} failed. Retrying...`
                  );
                  retryCount++;
                  setTimeout(attemptConnection, retryDelay);
                } else {
                  console.log(
                    "Max retry attempts reached. Redirecting to login..."
                  );
                  // Clear storage and reject with specific error
                  reject(new Error("MAX_RETRIES_REACHED"));
                }
              };

              ws.onerror = (error) => {
                console.error("WebSocket error:", error);
                setConnectionStatus("error");
                if (retryCount >= maxRetries) {
                  reject(new Error("MAX_RETRIES_REACHED"));
                }
              };
            }
          } else {
            resolve(socket);
          }
        };

        attemptConnection();
      });
    };
  }, [session, socket, reconnectAttempt]);

  const sendMessage = useCallback(
    async (message: any) => {
      if (!socket || socket.readyState !== WebSocket.OPEN) {
        // console.log("Socket not connected - attempting reconnect");
        // await initializeWebSocket();
        // throw new Error('Socket not connected');
      }

      return new Promise((resolve, reject) => {
        const messageId = Date.now().toString();
        const timeoutId = setTimeout(() => {
          messageHandlersRef.current.delete(messageId);
          // reject(new Error('Message timeout'));
        }, 300000);

        const handler = (event: MessageEvent) => {
          try {
            const response = JSON.parse(event.data);
            if (response.job === message.job) {
              clearTimeout(timeoutId);
              messageHandlersRef.current.delete(messageId);
              resolve(response);
            }
          } catch (error) {
            console.error("Error parsing response:", error);
          }
        };

        messageHandlersRef.current.set(messageId, handler);
        socket?.addEventListener("message", handler);

        try {
          socket?.send(JSON.stringify(message));
        } catch (error) {
          clearTimeout(timeoutId);
          messageHandlersRef.current.delete(messageId);
          reject(error);
        }
      });
    },
    [socket, initializeWebSocket]
  );

  const getPosts = useCallback(
    async (data: { workspace_name: string }) => {
      try {
        return new Promise((resolve, reject) => {
          if (!socket) {
            console.log("Socket not connected");
            return;
          }

          const messageHandler = (event: MessageEvent) => {
            try {
              const response = JSON.parse(event.data);
              if (response.job === "get_posts") {
                console.log("Planner get posts socket response:", response);
                socket.removeEventListener("message", messageHandler);
                resolve(response);
              }
            } catch (error) {
              socket.removeEventListener("message", messageHandler);
              reject(error);
            }
          };

          socket.addEventListener("message", messageHandler);

          sendMessage({
            job: "get_posts",
            workspace_name: data.workspace_name,
          });
        });
      } catch (error) {
        console.error("Error fetching posts:", error);
        throw error; // Re-throw to allow handling by caller
      }
    },
    [sendMessage, socket]
  );

  const deletePost = useCallback(
    async (data: any) => {
      try {
        return new Promise((resolve) => {
          sendMessage({
            job: "delete_post",
            post_id: data.post_id,
          });

          if (socket) {
            socket.onmessage = (event) => {
              const response = JSON.parse(event.data);

              // Validate job type
              if (response.job === "delete_post") {
                resolve(response);
              }
            };
          }
        });
      } catch (error) {
        console.error("Error deleting post:", error);
      }
    },
    [sendMessage]
  );

  const createPost = useCallback(
    async (data: any) => {
      try {
        console.log("formattedImages", data.images);
        // Format media as array of objects with id and data properties
        const formattedMedia = data.images.map((imageData: string) => ({
          id: "new",
          data: imageData,
        }));

        return new Promise((resolve) => {
          sendMessage({
            job: "create_post",
            workspace_name: data.workspace_name,
            type: data.type,
            media: formattedMedia,
            social: data.socials,
            caption: data.caption,
            ...(data.post_id && { id: data.post_id }),
            ...(data.ai_generated && {
              ai_generated: Boolean(data.ai_generated),
            }),
          });

          if (socket) {
            socket.onmessage = (event) => {
              const response = JSON.parse(event.data);

              // Validate job type
              if (response.job === "create_post") {
                console.log("Planner create post socket response:", response);
                resolve(response);
              }
            };
          }
        });
      } catch (error) {
        console.error("Error creating/updating post:", error);
      }
    },
    [sendMessage]
  );

  const createSchedule = useCallback(
    async (data: any) => {
      try {
        // Format media as array of objects with id and data properties
        const formattedMedia = data.images.map((imageData: string) => ({
          id: "new",
          data: imageData,
        }));

        // Convert datetime to timestamp if needed
        let timestamp = data.datetime;
        if (typeof data.datetime === "string") {
          timestamp = Math.floor(new Date(data.datetime).getTime() / 1000);
        }

        return new Promise((resolve) => {
          sendMessage({
            job: "create_post",
            workspace_name: data.workspace_name,
            type: data.type,
            timestamp: timestamp,
            media: formattedMedia,
            social: data.socials,
            caption: data.caption,
            ...(data.post_id && { id: data.post_id }),
            ...(data.ai_generated && {
              ai_generated: Boolean(data.ai_generated),
            }),
          });

          if (socket) {
            socket.onmessage = (event) => {
              const response = JSON.parse(event.data);

              // Validate job type
              if (response.job === "create_post") {
                console.log(
                  "Planner create schedule socket response:",
                  response
                );
                resolve(response);
              }
            };
          }
        });
      } catch (error) {
        console.error("Error creating schedule:", error);
      }
    },
    [sendMessage]
  );

  const editPost = useCallback(
    async (data: any) => {
      try {
        return new Promise((resolve) => {
          sendMessage({
            job: "create_post",
            workspace_name: data.workspace_name,
            type: data.type,
            images: data.images,
            social: JSON.stringify(data.socials),
            caption: data.caption,
            id: data.id,
            ...(data.ai_generated && {
              ai_generated: Boolean(data.ai_generated),
            }),
          });

          if (socket) {
            socket.onmessage = (event) => {
              const response = JSON.parse(event.data);

              // Validate job type
              if (response.job === "create_post") {
                resolve(response);
              }
            };
          }
        });
      } catch (error) {
        console.error("Error editing post:", error);
      }
    },
    [sendMessage]
  );

  const generateVideo = useCallback(
    async (data: any) => {
      try {
        return new Promise((resolve) => {
          sendMessage({
            job: "generate_video",
            prompt: data.prompt,
            workspace_name: selectedWorkspace,
            ...(data.edit && { edit: data.edit }),
          });

          if (socket) {
            socket.onmessage = async (event: MessageEvent) => {
              try {
                const response = JSON.parse(event.data);

                if (response.success) {
                  // Validate job type
                  if (response.job == "generate_video") {
                    setUser({
                      videoLoading: true,
                      countDown: 300,
                      walletTokens: response.wallet,
                    });

                    if (!response.success) {
                      console.error(
                        "Error in planner socket response:",
                        response.error
                      );
                      setUser({
                        videoLoading: false,
                        countDown: 0,
                      });
                      resolve(response);
                      return;
                    }

                    const videoData = JSON.parse(response.video) as {
                      code: number;
                      message: string;
                      request_id: string;
                      data: {
                        task_id: string;
                        task_status: string;
                        created_at: number;
                        updated_at: number;
                      };
                    };

                    if (!videoData.data?.task_id) {
                      console.error("No task ID received in response");
                      setUser({
                        videoLoading: false,
                        countDown: 0,
                      });
                      resolve(null);
                      return;
                    }

                    const postId = videoData.data?.task_id;
                    console.log("Planner socket response:", response, postId);

                    setVideoId(postId);

                    console.log(
                      "csrfToken for video status check: ",
                      csrfToken
                    );

                    // Set up polling interval for video status
                    const checkInterval = setInterval(async () => {
                      try {
                        const { data: statusData } = await axios.get(
                          `${process.env.NEXT_PUBLIC_API_URL}/api/kailing-hook/`,
                          {
                            params: { task_id: postId },
                            headers: {
                              accept: "application/json",
                              csrf_token: csrfToken,
                              Authorization: `Bearer ${access}`,
                            },
                          }
                        );

                        console.log(
                          "Video status check:",
                          statusData.data.task_status
                        );

                        // If video generation succeeded or there's an error message, clear the interval
                        if (
                          statusData.data.task_status === "succeed" ||
                          statusData.data.task_status_msg
                        ) {
                          console.log(
                            "Video result:",
                            statusData.data.task_result
                          );
                          setUser({
                            videoLoading: false,
                            countDown: 0,
                          });

                          if (
                            statusData.data.task_status_msg ||
                            !statusData.data.task_result?.videos?.[0]?.url
                          ) {
                            console.error(
                              "Error or missing URL in status response"
                            );
                            resolve(null);
                          } else {
                            resolve(statusData.data.task_result.videos[0].url);
                          }
                          clearInterval(checkInterval);
                        }
                      } catch (error) {
                        console.error("Error checking video status:", error);
                        setUser({
                          videoLoading: false,
                          countDown: 0,
                        });
                        clearInterval(checkInterval);
                        resolve(response);
                      }
                    }, 15000); // Check every 15 seconds

                    // Clean up interval on component unmount
                    return () => clearInterval(checkInterval);
                  }
                } else {
                  resolve(response);
                }
              } catch (error) {
                console.error("Error processing websocket message:", error);
                setUser({
                  videoLoading: false,
                  countDown: 0,
                });
                resolve(response);
              }
            };
          }
        });
      } catch (error) {
        console.error("Error generating video:", error);
      }
    },
    [sendMessage]
  );

  const generateCaption = useCallback(
    async (data: any) => {
      try {
        return new Promise((resolve, reject) => {
          // Store original message handler to restore later
          const originalOnMessage = socket?.onmessage;

          // Send message with consistent payload structure
          // Prefer a concrete workspace_name from details, fallback to selected string
          const workspaceName =
            (selectedWorkspaceDetails as any)?.workspace_name || selectedWorkspace;

          sendMessage({
            job: "generate_caption",
            prompt: data.prompt,
            type: data.type,
            workspace_name: workspaceName,
            ...(data.edit && { edit: data.edit }),
            ...(data.previous_answer && {
              previous_answer: data.previous_answer,
            }),
          });

          if (socket) {
            socket.onmessage = (event) => {
              try {
                const response = JSON.parse(event.data);

                if (response.job === "generate_caption") {
                  if (response.success) {
                    setUser({
                      walletTokens: response.wallet,
                      generatedCaption: response.text,
                    });
                    console.log("Caption generated successfully:", response);
                    resolve(response);
                  } else {
                    console.error("Caption generation failed:", response);
                    resolve(response);
                  }

                  // Restore original message handler
                  socket.onmessage = originalOnMessage;
                }
              } catch (error) {
                console.error("Error processing caption response:", error);
                reject(error);
                socket.onmessage = originalOnMessage;
              }
            };
          } else {
            reject(new Error("WebSocket connection not established"));
          }
        });
      } catch (error) {
        console.error("Error generating caption:", error);
        throw error; // Re-throw to allow handling by caller
      }
    },
    [sendMessage, socket, setUser]
  );

  const generateHashtags = useCallback(
    async (data: any) => {
      try {
        return new Promise((resolve) => {
          if (!data.edit) {
            sendMessage({
              job: "generate_hashtags",
              prompt: data.prompt,
              type: data.type,
              workspace_name: selectedWorkspace,
              ...(data.previous_answer && {
                previous_answer: data.previous_answer,
              }),
            });
          } else {
            sendMessage({
              job: "generate_hashtags",
              prompt: data.prompt,
              type: data.type,
              edit: data.edit,
              workspace_name: selectedWorkspace,
              ...(data.previous_answer && {
                previous_answer: data.previous_answer,
              }),
            });
          }

          if (socket) {
            socket.onmessage = (event) => {
              const response = JSON.parse(event.data);

              if (response.success) {
                if (response.job === "generate_hashtags") {
                  // Convert hashtag string to array by splitting on spaces
                  const hashtagArray = response.text.split(" ");

                  setUser({
                    walletTokens: response.wallet,
                    generatedHashtags: hashtagArray,
                  });
                  console.log("Planner socket response:", response);
                  resolve(response);
                }
              } else {
                resolve(response);
              }
            };
          }
        });
      } catch (error) {
        console.error("Error generating hashtags:", error);
      }
    },
    [sendMessage]
  );

  const generateImage = useCallback(
    async (data: any) => {
      try {
        return new Promise((resolve) => {
          sendMessage({
            job: "generate_image",
            prompt: data.prompt,
            workspace_name: selectedWorkspace,
            ...(data.edit && { edit: data.edit }),
            ...(data.type && { type: data.type }),
          });
          setUser({
            imageLoading: true,
            countDown: 30,
          });

          if (socket) {
            socket.onmessage = async (event: MessageEvent) => {
              try {
                const response = JSON.parse(event.data);

                // Validate job type
                if (response.job === "generate_image") {
                  setUser({
                    walletTokens: response.wallet,
                    imageLoading: false,
                    countDown: 0,
                  });

                  if (response.success) {
                    // Handle both direct URL and JSON-string payloads
                    if (response.image) {
                      // If image is a JSON string (e.g., { post_ids, post_status, errors })
                      if (typeof response.image === "string") {
                        const trimmed = response.image.trim();
                        if (
                          trimmed.startsWith("{") ||
                          trimmed.startsWith("[")
                        ) {
                          try {
                            const imgObj = JSON.parse(response.image);
                            if (
                              imgObj &&
                              (imgObj.post_ids || imgObj.post_status)
                            ) {
                              // Return a structured object so callers can handle in-progress state
                              resolve({
                                status: imgObj.post_status ?? "unknown",
                                postIds: imgObj.post_ids ?? [],
                                errors: imgObj.errors ?? [],
                                imageCredit: response.image_credit ?? null,
                              });
                              return;
                            }
                          } catch (_) {
                            // Fall through to treat it as a path/URL
                          }
                        }
                      }

                      // Treat as path/URL; use normalizeUrl helper to prefix if needed
                      const imageUrl = normalizeUrl(response.image);
                      resolve(imageUrl);
                      return;
                    }
                  } else {
                    resolve(response);
                  }
                }
              } catch (error) {
                console.error("Error processing websocket message:", error);
                setUser({
                  imageLoading: false,
                  countDown: 0,
                });
                resolve(error);
              }
            };
          }
        });
      } catch (error) {
        console.error("Error generating image:", error);
      }
    },
    [sendMessage]
  );

  const getLogs = useCallback(
    async (data: any) => {
      try {
        return new Promise((resolve) => {
          sendMessage({
            job: "get_logs",
            workspace_name: selectedWorkspace,
            target_email: data.target_email,
          });

          if (socket) {
            socket.onmessage = async (event: MessageEvent) => {
              const response = JSON.parse(event.data);

              // Validate job type
              if (response.job === "get_logs") {
                resolve(response);
              }
            };
          }
        });
      } catch (error) {
        console.error("Error fetching logs:", error);
      }
    },
    [sendMessage]
  );

  // User Websocket

  // const fetchCsrfToken = useCallback(async () => {
  //   const csrfResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/login/`, {
  //     method: 'GET',
  //   });
  //   const csrfData = await csrfResponse.json();
  //   return csrfData.csrfToken;
  // }, []);

  const getWorkspaces = useCallback(async () => {
    return new Promise(async (resolve, reject) => {
      try {
        if (socket && socket.readyState === WebSocket.OPEN) {
          sendMessage({
            csrf_token: csrfToken,
            job: "get_all_workspaces",
          });

          setUser({
            getWorkspaceDone: false,
          });

          socket.onmessage = (event) => {
            try {
              const response = JSON.parse(event.data);

              // Always set loading state and initialize arrays, regardless of response
              let typedWorkspaces: any[] = [];

              if (response.job === "get_all_workspaces") {
                console.log("Current workspaces:", workspaces);
                console.log("Received response from get workspaces:", response);

                // Map workspaces with strict type checking and fallbacks
                typedWorkspaces = response.workspaces
                  .map((workspace: any) => {
                    // Validate workspace object exists
                    if (!workspace) return null;

                    return {
                      id:
                        typeof workspace.id !== "undefined" ? workspace.id : "",
                      workspace_name:
                        typeof workspace.workspace_name === "string"
                          ? workspace.workspace_name
                          : "",
                      logo:
                        typeof workspace.logo === "string"
                          ? workspace.logo
                          : "",
                      workspace_members:
                        typeof workspace.workspace_members !== "undefined"
                          ? workspace.workspace_members
                          : "",
                      created_at:
                        typeof workspace.created_at === "string" ||
                        typeof workspace.created_at === "number"
                          ? workspace.created_at
                          : "",
                      last_activity:
                        typeof workspace.last_activity === "string"
                          ? workspace.last_activity
                          : "",
                      social_accounts:
                        typeof workspace.social_accounts !== "undefined"
                          ? workspace.social_accounts
                          : [],
                      industry:
                        typeof workspace.industry === "string"
                          ? workspace.industry
                          : "",
                      website:
                        typeof workspace.website === "string"
                          ? workspace.website
                          : "",
                      members: Array.isArray(workspace.members)
                        ? workspace.members
                        : [],
                      owner: workspace.owner,
                    };
                  })
                  .filter(Boolean); // Remove any null entries

                setUser({
                  workspaces: typedWorkspaces.map((w: any) => w.workspace_name),
                  workspacesDetails: typedWorkspaces,
                });

                setTimeout(() => {
                  setUser({
                    isLoading: false,
                    getWorkspaceDone: true,
                    initialLoad: false,
                  });
                }, 700);
              }

              // Always update state with typed workspaces (empty array if none)
              resolve(response);
            } catch (error) {
              // On error, still ensure state is updated with empty arrays
              console.error("Error parsing workspace response:", error);
              setUser({
                workspaces: [],
                workspacesDetails: [],
                isLoading: false,
                initialLoad: false,
              });
              reject(error);
            }
          };
        } else {
          console.warn("WebSocket is not open. Waiting for connection...");
        }
      } catch (error) {
        console.error("Error in getWorkspaces:", error);
        reject(error);
      }
    });
  }, [csrfToken, sendMessage, socket, workspacesDetails, selectedWorkspace]);

  interface WorkspaceData {
    workspace_name: string;
    [key: string]: any;
  }

  const getWorkspaceData = useCallback(
    async (data: WorkspaceData) => {
      try {
        if (socket && socket.readyState === WebSocket.OPEN) {
          sendMessage({
            csrf_token: csrfToken,
            job: "get_workspace",
            workspace_name: data.workspace_name,
          });

          socket.onmessage = (event) => {
            try {
              const response = JSON.parse(event.data);

              if (response.job === "get_workspace") {
                let updatedWorkspaces = [...workspacesDetails];
                if (
                  !workspacesDetails.some(
                    (w) => w.workspace_name === data.workspace_name
                  )
                ) {
                  updatedWorkspaces.push({
                    ...response.details,
                    workspace_name: data.workspace_name,
                  });
                }
                setUser({
                  workspacesDetails: updatedWorkspaces,
                  isLoading: false,
                  initialLoad: false,
                });
              }
            } catch (error) {
              console.error("Error parsing WebSocket response:", error);
            }
          };
        } else {
          console.warn("WebSocket is not open. Waiting for connection...");
        }
      } catch (error) {
        console.error("Error in getWorkspaces:", error);
      }
    },
    [csrfToken, sendMessage, socket, workspacesDetails]
  );

  interface WorkspaceMemberData {
    workspace_name: string;
    [key: string]: any;
  }

  const getWorkspaceMember = useCallback(
    async (data: WorkspaceMemberData) => {
      try {
        if (socket && socket.readyState === WebSocket.OPEN) {
          const originalOnMessage = socket.onmessage;

          sendMessage({
            csrf_token: csrfToken,
            job: "get_workspace_members",
            workspace_name: data.workspace_name,
          });

          socket.onmessage = (event) => {
            try {
              const response = JSON.parse(event.data);

              if (response.job === "get_workspace_members") {
                const updatedWorkspaces = [...workspacesDetails];
                const workspaceIndex = updatedWorkspaces.findIndex(
                  (w) => w.workspace_name === data.workspace_name
                );

                if (workspaceIndex !== -1) {
                  const updatedWorkspace = {
                    ...updatedWorkspaces[workspaceIndex],
                    members: response.members,
                  };
                  updatedWorkspaces[workspaceIndex] = updatedWorkspace;

                  setUser({
                    workspacesDetails: updatedWorkspaces,
                    isLoading: false,
                    initialLoad: false,
                  });

                  console.log("Workspace members Value: ", response);
                  console.log("workspacesDetails: ", workspacesDetails);
                }
              }
            } catch (error) {
              console.error("Error parsing WebSocket response:", error);
            } finally {
              socket.onmessage = originalOnMessage;
            }
          };
        }
      } catch (error) {
        console.error("Error in getWorkspaceMember:", error);
      }
    },
    [sendMessage, workspacesDetails, setUser]
  );

  const deleteWorkspaceMember = useCallback(
    async (data: { workspace_name: string; target_email: string }) => {
      try {
        // const csrfToken = await fetchCsrfToken();

        if (socket && socket.readyState === WebSocket.OPEN) {
          return new Promise((resolve, reject) => {
            const originalOnMessage = socket.onmessage;

            sendMessage({
              csrf_token: csrfToken,
              job: "delete_workspace_member",
              workspace_name: data.workspace_name,
              target_email: data.target_email,
            });

            socket.onmessage = (event) => {
              try {
                const response = JSON.parse(event.data);

                if (response.job === "delete_workspace_member") {
                  resolve(response);
                }
              } catch (error) {
                console.error("Error parsing WebSocket response:", error);
                reject(error);
              } finally {
                socket.onmessage = originalOnMessage;
              }
            };
          });
        } else {
          console.warn("WebSocket is not open. Waiting for connection...");
          throw new Error("WebSocket is not open");
        }
      } catch (error) {
        console.error("Error in deleteWorkspaceMember:", error);
        throw error;
      }
    },
    [csrfToken, sendMessage]
  );

  interface DashboardData {
    workspace_name: string;
    platform: string;
    device: string;
    [key: string]: any;
  }

  const getDashboard = useCallback(
    async (data: DashboardData) => {
      try {
        if (socket && socket.readyState === WebSocket.OPEN) {
          return new Promise((resolve, reject) => {
            const originalOnMessage = socket.onmessage;

            sendMessage({
              csrf_token: csrfToken,
              job: "get_dashboard",
              workspace_name: data.workspace_name,
              platform: data.platform,
              device: data.device,
            });

            socket.onmessage = (event) => {
              try {
                const response = JSON.parse(event.data);

                if (response.job === "get_dashboard") {
                  console.log("get dashboard response:", response);
                  resolve(response);
                }
              } catch (error) {
                console.error("Error parsing WebSocket response:", error);
                reject(error);
              } finally {
                socket.onmessage = originalOnMessage;
              }
            };
          });
        } else {
          console.warn("WebSocket is not open. Waiting for connection...");
          throw new Error("WebSocket is not open");
        }
      } catch (error) {
        console.error("Error in getDashboard:", error);
        throw error;
      }
    },
    [csrfToken, sendMessage]
  );

  const sendChangePassword = useCallback(async () => {
    try {
      // const csrfToken = await fetchCsrfToken();

      if (socket && socket.readyState === WebSocket.OPEN) {
        return new Promise((resolve, reject) => {
          const originalOnMessage = socket.onmessage;

          sendMessage({
            csrf_token: csrfToken,
            job: "password",
          });

          socket.onmessage = (event) => {
            try {
              const response = JSON.parse(event.data);
              console.log("get change password response:", response);
              resolve(response);
            } catch (error) {
              console.error("Error parsing WebSocket response:", error);
              reject(error);
            } finally {
              socket.onmessage = originalOnMessage;
            }
          };
        });
      } else {
        console.warn("WebSocket is not open. Waiting for connection...");
        throw new Error("WebSocket is not open");
      }
    } catch (error) {
      console.error("Error in getDashboard:", error);
      throw error;
    }
  }, [csrfToken, sendMessage]);

  const saveDashboard = useCallback(
    async (data) => {
      try {
        if (socket && socket.readyState === WebSocket.OPEN) {
          return new Promise((resolve, reject) => {
            const originalOnMessage = socket.onmessage;

            sendMessage({
              csrf_token: csrfToken,
              job: "save_dashboard",
              workspace_name: data.workspace_name,
              platform: data.platform,
              device: data.device,
              json: data.json,
            });

            socket.onmessage = (event) => {
              try {
                const response = JSON.parse(event.data);

                if (response.job === "save_dashboard") {
                  console.log("save dashboard response:", response);
                  resolve(response);
                }
              } catch (error) {
                console.error("Error parsing WebSocket response:", error);
                reject(error);
              } finally {
                socket.onmessage = originalOnMessage;
              }
            };
          });
        } else {
          console.warn("WebSocket is not open. Waiting for connection...");
          throw new Error("WebSocket is not open");
        }
      } catch (error) {
        console.error("Error in saveDashboard:", error);
        throw error;
      }
    },
    [csrfToken, sendMessage]
  );

  const createWorkspace = useCallback(
    async (data: {
      name: string;
      industry: string;
      website: string;
      members: string;
      logo: string;
    }) => {
      try {
        // const csrfToken = await fetchCsrfToken();
        console.log("csrfToken token:", csrfToken);

        const promise = new Promise(async (resolve, reject) => {
          if (socket) {
            sendMessage({
              csrf_token: csrfToken,
              job: "create_workspace",
              workspace_name: data.name,
              workspace_members: data.members,
              logo: data.logo,
              industry: data.industry,
              website: data.website,
            });

            const originalOnMessage = socket.onmessage;

            socket.onmessage = (event) => {
              try {
                const response = JSON.parse(event.data);

                if (response.job === "create_workspace") {
                  console.log("Create workspace response:", response);
                  resolve(response);

                  setTimeout(() => {
                    getWorkspaces();
                  }, 1500);
                }
              } catch (error) {
                console.error("Error parsing WebSocket response:", error);
                reject(error);
              } finally {
                socket.onmessage = originalOnMessage;
              }
            };
          }
        });

        return promise;
      } catch (error) {
        console.error("Error creating workspace:", error);
        throw error;
      }
    },
    [csrfToken, sendMessage]
  );

  const updateProfilews = useCallback(
    async (data: string) => {
      try {
        // const csrfToken = await fetchCsrfToken();
        console.log("csrfToken token:", csrfToken);

        const response = new Promise<any>((resolve) => {
          sendMessage({
            csrfToken: csrfToken,
            photo: data,
            job: "profile",
          });

          if (socket) {
            socket.onmessage = async (event) => {
              try {
                const response = JSON.parse(event.data);

                if (response.job === "profile") {
                  const photoUrl = normalizeUrl(response.message.photo.value);

                  setUser({
                    profilePhoto: photoUrl,
                  });

                  sessionStorage.setItem("userProfilePhoto", photoUrl);

                  resolve(response);
                }
              } catch (error) {
                console.error("Error parsing WebSocket response:", error);
                resolve(null);
              }
            };
          }
        });

        console.log("Photo uploaded");
        return response;
      } catch (error) {
        console.error("Error updating profile:", error);
        throw error;
      }
    },
    [csrfToken, sendMessage, socket]
  );

  const getWalletValue = useCallback(async () => {
    try {
      // const csrfToken = await fetchCsrfToken();
      console.log("csrfToken token:", csrfToken);

      sendMessage({
        csrfToken: csrfToken,
        job: "get_wallet",
      });

      if (socket) {
        socket.onmessage = (event) => {
          const response = JSON.parse(event.data);

          // Validate job type
          if (response.job === "get_wallet") {
            // Process wallet value response
            console.log("Wallet value response:", response);
          }
        };
      }
    } catch (error) {
      console.error("Error fetching wallet value:", error);
    }
  }, [csrfToken, sendMessage]);

  const sendSelectedWorkspace = useCallback(
    async (data: string) => {
      try {
        // const csrfToken = await fetchCsrfToken();
        console.log("csrfToken token:", csrfToken);

        sendMessage({
          csrfToken: csrfToken,
          job: "select_workspace",
          workspace_name: data,
        });

        if (socket) {
          socket.onmessage = (event) => {
            const response = JSON.parse(event.data);

            // Validate job type
            if (response.job === "select_workspace") {
              console.log("select workspace response:", response);
              setUser({
                selectedWorkspace: data,
              });
            }
          };
        }
      } catch (error) {
        console.error("Error selecting workspace:", error);
      }
    },
    [csrfToken, sendMessage]
  );

  const getWebSocketData = useCallback(() => {
    return webSocketData;
  }, [webSocketData]);

  const changeName = useCallback(
    async (data: any) => {
      try {
        // const csrfToken = await fetchCsrfToken();
        console.log("csrfToken token:", csrfToken);

        sendMessage({
          csrfToken: csrfToken,
          first_name: data.first_name,
          last_name: data.last_name,
          job: "profile",
        });

        if (socket) {
          socket.onmessage = (event) => {
            const response = JSON.parse(event.data);

            // Validate job type
            if (response.job === "profile") {
              setUser({
                firstName: response.message.first_name.value,
                lastName: response.message.last_name.value,
              });
              console.log("WebSocket for name change response:", response);
            }
          };
        }
      } catch (error) {
        console.error("Error changing name:", error);
      }
    },
    [csrfToken, sendMessage]
  );

  const deleteWorkspace = useCallback(
    async (data: any) => {
      try {
        // const csrfToken = await fetchCsrfToken();
        console.log("csrfToken token:", csrfToken);

        sendMessage({
          csrfToken: csrfToken,
          workspace_name: data.workspace_name,
          job: "delete_workspace",
        });

        if (socket) {
          socket.onmessage = (event) => {
            const response = JSON.parse(event.data);

            // Validate job type
            if (response.job === "delete_workspace") {
              if (response.success) {
                setUser({
                  selectedWorkspace: "",
                  selectedWorkspaceDetails: {},
                  workspaceLogo: "",
                  selectedSocial: {},
                });

                getWorkspaces();
                setTimeout(() => {
                  getProfile();
                }, 500);
              }
            }

            // After successful deletion, refresh workspaces list
            setTimeout(() => {
              getWorkspaces();
            }, 1000);
          };
        }
      } catch (error) {
        console.error("Error deleting workspace:", error);
      }
    },
    [csrfToken, sendMessage]
  );

  const updateWorkspace = useCallback(
    async (data: any) => {
      try {
        // const csrfToken = await fetchCsrfToken();
        console.log("csrfToken token:", csrfToken);

        sendMessage({
          csrf_token: csrfToken,
          job: "update_workspace",
          workspace_id: data.id,
          workspace_name: data.workspace_name,
          logo: data.logo,
          industry: data.industry,
          website: data.website,
          workspace_members: data.members,
        });

        if (socket) {
          socket.onmessage = (event) => {
            const response = JSON.parse(event.data);

            // Validate job type
            if (response.job === "update_workspace") {
              if (response.workspaces) {
                const typedWorkspaces = response.workspaces
                  .map((workspace: any) => {
                    // Validate workspace object exists
                    if (!workspace) return null;

                    return {
                      id:
                        typeof workspace.id !== "undefined" ? workspace.id : "",
                      workspace_name:
                        typeof workspace.workspace_name === "string"
                          ? workspace.workspace_name
                          : "",
                      logo:
                        typeof workspace.logo === "string"
                          ? workspace.logo
                          : "",
                      workspace_members:
                        typeof workspace.workspace_members !== "undefined"
                          ? workspace.workspace_members
                          : "",
                      created_at:
                        typeof workspace.created_at === "string" ||
                        typeof workspace.created_at === "number"
                          ? workspace.created_at
                          : "",
                      last_activity:
                        typeof workspace.last_activity === "string"
                          ? workspace.last_activity
                          : "",
                      social_accounts:
                        typeof workspace.social_accounts !== "undefined"
                          ? workspace.social_accounts
                          : [],
                      industry:
                        typeof workspace.industry === "string"
                          ? workspace.industry
                          : "",
                      website:
                        typeof workspace.website === "string"
                          ? workspace.website
                          : "",
                      members: Array.isArray(workspace.members)
                        ? workspace.members
                        : [],
                      owner: workspace.owner,
                    };
                  })
                  .filter(Boolean); // Remove any null entries

                setUser({
                  workspaces: typedWorkspaces.map((w: any) => w.workspace_name),
                  workspacesDetails: typedWorkspaces,
                  isLoading: false,
                  initialLoad: false,
                });

                console.log("res of get workspaces:", response);
                const selectedWorkspace2 = response.workspaces.find(
                  (workspace: any) =>
                    workspace.workspace_name === data.workspace_name
                );

                if (selectedWorkspace2) {
                  // First update workspace details
                  setUser({
                    selectedWorkspace: data.workspace_name,
                    selectedWorkspaceDetails: selectedWorkspace2,
                    workspaceLogo: selectedWorkspace2.logo
                      ? `${process.env.NEXT_PUBLIC_API_URL}${selectedWorkspace2.logo}`
                      : "/default-workspace-logo.svg",
                  });

                  // Then handle social accounts if they exist
                  if (selectedWorkspace2.social_accounts?.length > 0) {
                    setUser({
                      selectedSocial:
                        selectedWorkspace2.social_accounts[
                          selectedWorkspace2.social_accounts.length - 1
                        ],
                    });
                  }
                }

                console.log("res of update workspaces:", response);
              }
            }
          };
        }
      } catch (error) {
        console.error("Error updating workspace:", error);
      }
    },
    [csrfToken, sendMessage]
  );
  const addWorkspaceMember = useCallback(
    async (data: any) => {
      return new Promise((resolve, reject) => {
        try {
          console.log("csrfToken token:", csrfToken);

          sendMessage({
            csrf_token: csrfToken,
            job: "add_workspace_member",
            workspace_name: data.workspace_name,
            target_email: data.email,
          });

          if (socket) {
            socket.onmessage = (event) => {
              const response = JSON.parse(event.data);

              // Validate job type
              if (response.job === "add_workspace_member") {
                console.log("add workspace member response:", response);
                resolve(response);
              }
            };
          } else {
            reject(new Error("Socket not initialized"));
          }
        } catch (error) {
          console.error("Error adding workspace member:", error);
          reject(error);
        }
      });
    },
    [csrfToken, sendMessage]
  );

  const getTokenRate = useCallback(
    async (data?: any) => {
      try {
        // const csrfToken = await fetchCsrfToken();
        console.log("csrfToken token:", csrfToken);

        sendMessage({
          job: "get_token_rate",
        });

        if (socket) {
          socket.onmessage = (event) => {
            const response = JSON.parse(event.data);

            if (response.job == "get_token_rate") {
              console.log("response of token rate:", response?.token_rate);
              setUser({
                tokenRate:
                  response && response.token_rate
                    ? response.token_rate.toString()
                    : "0",
              });
            }
          };
        }
      } catch (error) {
        console.error("Error fetching token rate:", error);
      }
    },
    [csrfToken, sendMessage]
  );

  const acceptInvite = useCallback(
    (data: { invite_code: string }) => {
      return new Promise((resolve, reject) => {
        try {
          sendMessage({
            job: "confirm_workspace_invite",
            invite_code: data.invite_code,
          });

          if (socket) {
            socket.onmessage = (event) => {
              const response = JSON.parse(event.data);

              if (response.job === "confirm_workspace_invite") {
                console.log("response of invite member:", response);
                resolve(response);
              }
            };
          } else {
            reject(new Error("Socket not initialized"));
          }
        } catch (error) {
          console.error("Error in acceptInvite:", error);
          reject(error);
        }
      });
    },
    [sendMessage]
  );

  const getComments = useCallback(
    async (data: any) => {
      setUser({
        inboxLoading: true,
      });
      try {
        return new Promise((resolve, reject) => {
          let timeoutId: NodeJS.Timeout;

          const messageHandler = (event: MessageEvent) => {
            try {
              const response = JSON.parse(event.data);

              if (response.job === "get_posts_comments") {
                console.log("response of get posts comments:", response);
                clearTimeout(timeoutId);
                socket?.removeEventListener("message", messageHandler);
                setUser({
                  inboxLoading: false,
                });
                resolve(response);
              }
            } catch (error) {
              clearTimeout(timeoutId);
              socket?.removeEventListener("message", messageHandler);
              setUser({
                inboxLoading: false,
              });
              reject(error);
            }
          };

          if (socket) {
            socket.addEventListener("message", messageHandler);

            // Set timeout to reject if no response received
            timeoutId = setTimeout(() => {
              socket.removeEventListener("message", messageHandler);
              setUser({
                inboxLoading: false,
              });
              reject(
                new Error("Timeout waiting for get_posts_comments response")
              );
            }, 30000); // 30 second timeout

            sendMessage({
              job: "get_posts_comments",
              workspace_name: data.workspace_name,
              limit: "10",
              ...(data.next && { after: data.next }),
              ...(data.before && { before: data.before }),
            });
          } else {
            reject(new Error("Socket not initialized"));
          }
        });
      } catch (error) {
        console.error("Error fetching comments:", error);
        setUser({
          inboxLoading: false,
        });
        throw error;
      }
    },
    [sendMessage, setUser]
  );

  const getPostComments = useCallback(
    async (data: any) => {
      try {
        return new Promise((resolve, reject) => {
          let timeoutId: NodeJS.Timeout;

          const messageHandler = (event: MessageEvent) => {
            const response = JSON.parse(event.data);

            if (response.job === "get_comments") {
              console.log("response of get comments:", response);
              clearTimeout(timeoutId);
              socket?.removeEventListener("message", messageHandler);
              setUser({
                inboxLoading: false,
              });
              resolve(response);
            }
          };

          if (socket) {
            socket.addEventListener("message", messageHandler);

            // Set timeout to reject if no response received
            timeoutId = setTimeout(() => {
              socket.removeEventListener("message", messageHandler);
              setUser({
                inboxLoading: false,
              });
              reject(new Error("Timeout waiting for get_comments response"));
            }, 30000); // 30 second timeout

            sendMessage({
              job: "get_comments",
              workspace_name: data.workspace_name,
              limit: "10",
              ...(data.next && { after: data.next }),
              ...(data.before && { before: data.before }),
              post_id: data.post_id,
              platform: data.platform || "instagram", // Add platform parameter with instagram as default
            });
          } else {
            reject(new Error("Socket not initialized"));
          }
        });
      } catch (error) {
        console.error("Error fetching comments:", error);
      }
    },
    [sendMessage, setUser]
  );

  const postComment = useCallback(
    async (data: any) => {
      try {
        return new Promise((resolve) => {
          const messageHandler = (event: MessageEvent) => {
            const response = JSON.parse(event.data);

            if (response.job === "post_comment") {
              console.log("response of post comment:", response);
              socket?.removeEventListener("message", messageHandler);

              resolve(response);
            }
          };

          if (socket) {
            socket.addEventListener("message", messageHandler);
          }

          sendMessage({
            job: "post_comment",
            workspace_name: data.workspace_name,
            post_id: data.post_id,
            text: data.text,
            social: data.social_id,
            platform: data.platform || "instagram", // Add platform parameter with instagram as default
          });
        });
      } catch (error) {
        console.error("Error fetching comments:", error);
      }
    },
    [sendMessage, setUser]
  );

  const getEditAccess = useCallback(
    async (data: any) => {
      try {
        return new Promise((resolve) => {
          const messageHandler = (event: MessageEvent) => {
            const response = JSON.parse(event.data);

            if (response.job === "check_access") {
              console.log("response of check access:", response);
              socket?.removeEventListener("message", messageHandler);

              resolve(response);
            }
          };

          if (socket) {
            socket.addEventListener("message", messageHandler);
          }

          sendMessage({
            job: "check_access",
            workspace_name: data.workspace_name,
            target_email: data.target_email,
          });
        });
      } catch (error) {
        console.error("Error fetching comments:", error);
      }
    },
    [sendMessage]
  );

  const editAccess = useCallback(
    async (data: any) => {
      try {
        return new Promise((resolve) => {
          const messageHandler = (event: MessageEvent) => {
            const response = JSON.parse(event.data);

            if (response.job === "add_access") {
              console.log("response of edit access:", response);
              socket?.removeEventListener("message", messageHandler);

              resolve(response);
            }
          };

          if (socket) {
            socket.addEventListener("message", messageHandler);
          }

          sendMessage({
            job: "add_access",
            workspace_name: data.workspace_name,
            social_id: data.social_id,
            target_email: data.target_email,
          });
        });
      } catch (error) {
        console.error("Error fetching comments:", error);
      }
    },
    [sendMessage]
  );

  const toggleAddAccess = useCallback(
    async (data: any) => {
      try {
        return new Promise((resolve) => {
          const messageHandler = (event: MessageEvent) => {
            const response = JSON.parse(event.data);

            if (response.job === "toggle_add_access") {
              console.log("response of toggle add access:", response);
              socket?.removeEventListener("message", messageHandler);

              resolve(response);
            }
          };

          if (socket) {
            socket.addEventListener("message", messageHandler);
          }

          sendMessage({
            job: "toggle_add_access",
            workspace_name: data.workspace_name,
            target_email: data.target_email,
          });
        });
      } catch (error) {
        console.error("Error fetching comments:", error);
      }
    },
    [sendMessage]
  );

  const toggleDeleteAccess = useCallback(
    async (data: any) => {
      try {
        return new Promise((resolve) => {
          const messageHandler = (event: MessageEvent) => {
            const response = JSON.parse(event.data);

            if (response.job === "toggle_delete_access") {
              console.log("response of toggle delete access:", response);
              socket?.removeEventListener("message", messageHandler);

              resolve(response);
            }
          };

          if (socket) {
            socket.addEventListener("message", messageHandler);
          }

          sendMessage({
            job: "toggle_delete_access",
            workspace_name: data.workspace_name,
            target_email: data.target_email,
          });
        });
      } catch (error) {
        console.error("Error fetching comments:", error);
      }
    },
    [sendMessage]
  );

  const deleteAccess = useCallback(
    async (data: any) => {
      try {
        return new Promise((resolve) => {
          const messageHandler = (event: MessageEvent) => {
            const response = JSON.parse(event.data);

            if (response.job === "delete_access") {
              console.log("response of delete access:", response);
              socket?.removeEventListener("message", messageHandler);

              resolve(response);
            }
          };

          if (socket) {
            socket.addEventListener("message", messageHandler);
          }

          sendMessage({
            job: "delete_access",
            workspace_name: data.workspace_name,
            social_id: data.social_id,
            target_email: data.target_email,
          });
        });
      } catch (error) {
        console.error("Error fetching comments:", error);
      }
    },
    [sendMessage]
  );

  const getAutoAnswer = useCallback(
    async (data: any) => {
      if (!socket || socket.readyState !== WebSocket.OPEN) {
        console.log("Socket not connected or not open");
        return {};
      }

      try {
        return new Promise((resolve) => {
          let timeoutId: NodeJS.Timeout;

          // Create a one-time message handler
          const messageHandler = (event: MessageEvent) => {
            try {
              const response = JSON.parse(event.data);

              if (response.job === "get_autoanswer") {
                // Clean up
                clearTimeout(timeoutId);
                socket.removeEventListener("message", messageHandler);
                resolve(response);
              }
            } catch (error) {
              console.error("Error parsing WebSocket response:", error);
              socket.removeEventListener("message", messageHandler);
              resolve({});
            }
          };

          // Add the event listener before sending the message
          socket.addEventListener("message", messageHandler);

          // Send the message
          sendMessage({
            job: "get_autoanswer",
            workspace_name: data.workspace_name,
          }).catch(() => {
            socket.removeEventListener("message", messageHandler);
            resolve({});
          });

          // Set a timeout to remove the listener if no response is received
          timeoutId = setTimeout(() => {
            socket.removeEventListener("message", messageHandler);
            resolve({}); // Resolve with empty object if no response
          }, 10000); // 10 second timeout
        });
      } catch (error) {
        console.error("Error in getAutoAnswer:", error);
        return {};
      }
    },
    [sendMessage]
  );

  const enableAutoAnswers = useCallback(
    async (data: { workspace_name: string }) => {
      if (!socket || socket.readyState !== WebSocket.OPEN) {
        console.log("Socket not connected or not open");
        return { success: false };
      }

      try {
        return new Promise((resolve) => {
          let timeoutId: NodeJS.Timeout;

          // Create a one-time message handler
          const messageHandler = (event: MessageEvent) => {
            try {
              const response = JSON.parse(event.data);

              if (response.job === "enable_autoanswers") {
                // Clean up
                clearTimeout(timeoutId);
                socket.removeEventListener("message", messageHandler);
                resolve(response);
              }
            } catch (error) {
              console.error("Error parsing WebSocket response:", error);
              socket.removeEventListener("message", messageHandler);
              resolve({ success: false });
            }
          };

          // Add the event listener before sending the message
          socket.addEventListener("message", messageHandler);

          // Send the message
          sendMessage({
            job: "enable_autoanswers",
            workspace_name: data.workspace_name,
          }).catch(() => {
            socket.removeEventListener("message", messageHandler);
            resolve({ success: false });
          });

          // Set a timeout to remove the listener if no response is received
          timeoutId = setTimeout(() => {
            socket.removeEventListener("message", messageHandler);
            resolve({ success: false }); // Resolve with failure if no response
          }, 10000); // 10 second timeout
        });
      } catch (error) {
        console.error("Error in enableAutoAnswers:", error);
        return { success: false };
      }
    },
    [sendMessage, socket]
  );

  const disableAutoAnswers = useCallback(
    async (data: { workspace_name: string }) => {
      if (!socket || socket.readyState !== WebSocket.OPEN) {
        console.log("Socket not connected or not open");
        return { success: false };
      }

      try {
        return new Promise((resolve) => {
          let timeoutId: NodeJS.Timeout;

          // Create a one-time message handler
          const messageHandler = (event: MessageEvent) => {
            try {
              const response = JSON.parse(event.data);

              if (response.job === "disable_autoanswers") {
                // Clean up
                clearTimeout(timeoutId);
                socket.removeEventListener("message", messageHandler);
                resolve(response);
              }
            } catch (error) {
              console.error("Error parsing WebSocket response:", error);
              socket.removeEventListener("message", messageHandler);
              resolve({ success: false });
            }
          };

          // Add the event listener before sending the message
          socket.addEventListener("message", messageHandler);

          // Send the message
          sendMessage({
            job: "disable_autoanswers",
            workspace_name: data.workspace_name,
          }).catch(() => {
            socket.removeEventListener("message", messageHandler);
            resolve({ success: false });
          });

          // Set a timeout to remove the listener if no response is received
          timeoutId = setTimeout(() => {
            socket.removeEventListener("message", messageHandler);
            resolve({ success: false }); // Resolve with failure if no response
          }, 10000); // 10 second timeout
        });
      } catch (error) {
        console.error("Error in disableAutoAnswers:", error);
        return { success: false };
      }
    },
    [sendMessage, socket]
  );

  const editAutoAnswer = useCallback(
    async (data: any) => {
      try {
        return new Promise((resolve) => {
          const messageHandler = (event: MessageEvent) => {
            const response = JSON.parse(event.data);

            if (response.job === "edit_autoanswer") {
              console.log("response of edit autoanswer:", response);
              socket?.removeEventListener("message", messageHandler);

              resolve(response);
            }
          };

          if (socket) {
            socket.addEventListener("message", messageHandler);
          }

          // Make sure we always have a valid timezone
          const timezone =
            data.timezone || Intl.DateTimeFormat().resolvedOptions().timeZone;

          sendMessage({
            job: "edit_autoanswer",
            workspace_name: data.workspace_name,
            autoanswer_id: data.autoanswer_id,
            day: data.day,
            start_time: data.start_time,
            end_time: data.end_time,
            timezone: timezone,
            social_media_ids: data.social_media_ids,
            is_closed: data.is_closed,
          });
        });
      } catch (error) {
        console.error("Error handling auto-answer:", error);
      }
    },
    [sendMessage]
  );

  const deleteAutoAnswer = useCallback(
    async (data: any) => {
      try {
        return new Promise((resolve) => {
          const messageHandler = (event: MessageEvent) => {
            const response = JSON.parse(event.data);

            if (response.job === "create_autoanswer") {
              console.log("response of create autoanswer:", response);
              socket?.removeEventListener("message", messageHandler);

              resolve(response);
            }
          };

          if (socket) {
            socket.addEventListener("message", messageHandler);
          }

          sendMessage({
            job: "delete_autoanswer",
            workspace_name: data.workspace_name,
            autoanswer_id: data.autoanswer_id,
          });
        });
      } catch (error) {
        console.error("Error fetching comments:", error);
      }
    },
    [sendMessage]
  );

  const sendDirectMessage = useCallback(
    async (data: any) => {
      try {
        return new Promise((resolve) => {
          const messageHandler = (event: MessageEvent) => {
            const response = JSON.parse(event.data);

            if (response.job === "send_inbox_message") {
              console.log("response of post comment:", response);
              socket?.removeEventListener("message", messageHandler);

              resolve(response);
            }
          };

          if (socket) {
            socket.addEventListener("message", messageHandler);
          }

          sendMessage({
            job: "send_inbox_message",
            workspace_name: data.workspace_name,
            target_id: String(data.target_id),
            text: data.text,
            platform: data.platform,
            social: data.social_id,
            ...(data.media_base64 ? { media_base64: data.media_base64 } : {}),
          });
        });
      } catch (error) {
        console.error("Error fetching comments:", error);
      }
    },
    [sendMessage]
  );

  const createSavedReply = useCallback(
    async (data: any) => {
      try {
        return new Promise((resolve) => {
          const messageHandler = (event: MessageEvent) => {
            const response = JSON.parse(event.data);

            if (response.job === "create_saved_reply") {
              console.log("response of create saved reply:", response);
              socket?.removeEventListener("message", messageHandler);

              resolve(response);
            }
          };

          if (socket) {
            socket.addEventListener("message", messageHandler);
          }

          sendMessage({
            job: "create_saved_reply",
            workspace_name: data.workspace_name,
            title: data.title,
            text: data.text,
          });
        });
      } catch (error) {
        console.error("Error fetching comments:", error);
      }
    },
    [sendMessage]
  );

  const deleteSavedReply = useCallback(
    async (data: any) => {
      try {
        return new Promise((resolve) => {
          const messageHandler = (event: MessageEvent) => {
            const response = JSON.parse(event.data);

            if (response.job === "delete_saved_reply") {
              console.log("response of delete saved reply:", response);
              socket?.removeEventListener("message", messageHandler);

              resolve(response);
            }
          };

          if (socket) {
            socket.addEventListener("message", messageHandler);
          }

          sendMessage({
            job: "delete_saved_reply",
            workspace_name: data.workspace_name,
            title: data.title,
          });
        });
      } catch (error) {
        console.error("Error fetching comments:", error);
      }
    },
    [sendMessage]
  );

  const getAllSavedReply = useCallback(
    async (data: any) => {
      try {
        return new Promise((resolve) => {
          const messageHandler = (event: MessageEvent) => {
            const response = JSON.parse(event.data);

            if (response.job === "get_saved_reply") {
              console.log("response of get saved reply:", response);
              socket?.removeEventListener("message", messageHandler);

              resolve(response);
            }
          };

          if (socket) {
            socket.addEventListener("message", messageHandler);
          }

          sendMessage({
            job: "get_saved_reply",
            workspace_name: data.workspace_name,
          });
        });
      } catch (error) {
        console.error("Error fetching comments:", error);
      }
    },
    [sendMessage]
  );

  const editTitleSavedReply = useCallback(
    async (data: any) => {
      try {
        return new Promise((resolve) => {
          const messageHandler = (event: MessageEvent) => {
            const response = JSON.parse(event.data);

            if (response.job === "edit_saved_reply") {
              console.log("response of edit saved reply:", response);
              socket?.removeEventListener("message", messageHandler);

              resolve(response);
            }
          };

          if (socket) {
            socket.addEventListener("message", messageHandler);
          }

          sendMessage({
            job: "edit_saved_reply",
            workspace_name: data.workspace_name,
            title: data.title,
            new_title: data.new_title,
            text: data.text,
          });
        });
      } catch (error) {
        console.error("Error fetching comments:", error);
      }
    },
    [sendMessage]
  );

  const editTextSavedReply = useCallback(
    async (data: any) => {
      try {
        return new Promise((resolve) => {
          const messageHandler = (event: MessageEvent) => {
            const response = JSON.parse(event.data);

            if (response.job === "edit_saved_reply") {
              console.log("response of edit saved reply:", response);
              socket?.removeEventListener("message", messageHandler);

              resolve(response);
            }
          };

          if (socket) {
            socket.addEventListener("message", messageHandler);
          }

          sendMessage({
            job: "edit_saved_reply",
            workspace_name: data.workspace_name,
            title: data.title,
            text: data.text,
          });
        });
      } catch (error) {
        console.error("Error fetching comments:", error);
      }
    },
    [sendMessage]
  );

  const sendReact = useCallback(
    async (data: {
      workspace_name: string;
      target_id: string;
      reaction: string;
      message_id: string;
      social_id: string;
      unreact?: boolean;
    }) => {
      try {
        return new Promise((resolve) => {
          const messageHandler = (event: MessageEvent) => {
            const response = JSON.parse(event.data);

            if (response.job === "react_to_message") {
              console.log("Response of message reaction:", response);
              socket?.removeEventListener("message", messageHandler);
              resolve(response);
            }
          };

          if (socket) {
            socket.addEventListener("message", messageHandler);
          }

          sendMessage({
            job: "react_to_message",
            workspace_name: data.workspace_name,
            target_id: data.target_id,
            reaction: data.reaction,
            message_id: data.message_id,
            social: data.social_id,
            unreact: data.unreact || false,
          });
        });
      } catch (error) {
        console.error("Error sending reaction:", error);
      }
    },
    [sendMessage]
  );

  const replyToComment = useCallback(
    async (data: any) => {
      try {
        return new Promise((resolve) => {
          const messageHandler = (event: MessageEvent) => {
            const response = JSON.parse(event.data);

            if (response.job === "reply_to_comment") {
              console.log("response of get comments:", response);
              socket?.removeEventListener("message", messageHandler);

              resolve(response);
            }
          };

          if (socket) {
            socket.addEventListener("message", messageHandler);
          }

          sendMessage({
            job: "reply_to_comment",
            workspace_name: data.workspace_name,
            comment_id: data.comment_id,
            text: data.text,
            social: data.social_id,
            platform: data.platform || "instagram", // Add platform parameter with instagram as default
          });
        });
      } catch (error) {
        console.error("Error fetching comments:", error);
      }
    },
    [sendMessage, setUser]
  );

  const deleteComment = useCallback(
    async (data: any) => {
      setUser({
        inboxLoading: true,
      });
      try {
        return new Promise((resolve) => {
          const messageHandler = (event: MessageEvent) => {
            const response = JSON.parse(event.data);

            if (response.job === "delete_comment") {
              console.log("response of get comments:", response);
              socket?.removeEventListener("message", messageHandler);

              resolve(response);
            }
          };

          if (socket) {
            socket.addEventListener("message", messageHandler);
          }

          sendMessage({
            job: "delete_comment",
            workspace_name: data.workspace_name,
            social: data.social_id,
            comment_id: data.comment_id,
            platform: data.platform || "instagram", // Add platform parameter with instagram as default
          });
        });
      } catch (error) {
        console.error("Error fetching comments:", error);
      }
    },
    [sendMessage, setUser]
  );

  const getDirect = useCallback(
    async (data: any) => {
      try {
        return new Promise((resolve) => {
          const messageHandler = (event: MessageEvent) => {
            const response = JSON.parse(event.data);

            if (response.job === "get_conversations") {
              console.log("response of get inbox:", response);
              socket?.removeEventListener("message", messageHandler);
              resolve(response);
            }
          };

          if (socket) {
            socket.addEventListener("message", messageHandler);
          }

          sendMessage({
            job: "get_conversations",
            workspace_name: data.workspace_name,
            limit: "10",
            ...(data.next && { after: data.next }),
            ...(data.before && { before: data.before }),
          });
        });
      } catch (error) {
        console.error("Error fetching inbox:", error);
      }
    },
    [sendMessage]
  );

  const getDirectMessages = useCallback(
    async (data: any) => {
      try {
        return new Promise((resolve) => {
          const messageHandler = (event: MessageEvent) => {
            const response = JSON.parse(event.data);

            if (response.job === "get_conversation_messages") {
              console.log("response of get inbox:", response);
              socket?.removeEventListener("message", messageHandler);
              resolve(response);
            }
          };

          if (socket) {
            socket.addEventListener("message", messageHandler);
          }

          sendMessage({
            job: "get_conversation_messages",
            workspace_name: data.workspace_name,
            limit: "10",
            conversation_id: data.conversation_id,
            platform: data.platform || "instagram", // Add platform parameter with instagram as default
            ...(data.after && { after: data.after }),
            ...(data.before && { before: data.before }),
          });
        });
      } catch (error) {
        console.error("Error fetching inbox:", error);
      }
    },
    [sendMessage]
  );

  const createGoal = useCallback(
    async (data: any) => {
      try {
        return new Promise((resolve) => {
          const messageHandler = (event: MessageEvent) => {
            const response = JSON.parse(event.data);

            if (response.job === "create_goal") {
              console.log("Response from create goal:", response);
              socket?.removeEventListener("message", messageHandler);
              resolve(response);
            }
          };

          if (socket) {
            socket.addEventListener("message", messageHandler);
          }

          // Ensure social is a single ID (not an array)
          const socialId = Array.isArray(data.social)
            ? data.social[0]
            : data.social;

          sendMessage({
            job: "create_goal",
            workspace_name: data.workspace_name,
            icon: data.icon,
            name: data.name,
            duration: data.duration,
            social: [socialId],
            js_data: data.js_data || {},
          });
        });
      } catch (error) {
        console.error("Error creating goal:", error);
      }
    },
    [sendMessage]
  );

  const getGoals = useCallback(
    async (data: any) => {
      try {
        return new Promise((resolve) => {
          const messageHandler = (event: MessageEvent) => {
            const response = JSON.parse(event.data);

            if (response.job === "get_goals") {
              console.log("Response from get goals:", response);
              socket?.removeEventListener("message", messageHandler);
              resolve(response);
            }
          };

          if (socket) {
            socket.addEventListener("message", messageHandler);
          }

          sendMessage({
            job: "get_goals",
            workspace_name: data.workspace_name,
            social: data.social,
            is_active: data.is_active,
          });
        });
      } catch (error) {
        console.error("Error getting goals:", error);
      }
    },
    [sendMessage]
  );

  const updateGoal = useCallback(
    async (data: any) => {
      try {
        return new Promise((resolve) => {
          const messageHandler = (event: MessageEvent) => {
            const response = JSON.parse(event.data);

            if (response.job === "update_goal") {
              console.log("Response from update goal:", response);
              socket?.removeEventListener("message", messageHandler);
              resolve(response);
            }
          };

          if (socket) {
            socket.addEventListener("message", messageHandler);
          }

          sendMessage({
            job: "update_goal",
            goal_id: data.goal_id,
            workspace_name: data.workspace_name,
            is_active: data.is_active,
            icon: data.icon,
            name: data.name,
            start: data.start,
            end: data.end,
            duration: data.duration,
            js_data: data.js_data,
            social: data.social,
            is_pinned: data.is_pinned,
          });
        });
      } catch (error) {
        console.error("Error updating goal:", error);
      }
    },
    [sendMessage]
  );

  const deleteGoal = useCallback(
    async (data: any) => {
      try {
        return new Promise((resolve) => {
          const messageHandler = (event: MessageEvent) => {
            const response = JSON.parse(event.data);

            if (response.job === "delete_goal") {
              console.log("Response from delete goal:", response);
              socket?.removeEventListener("message", messageHandler);
              resolve(response);
            }
          };

          if (socket) {
            socket.addEventListener("message", messageHandler);
          }

          sendMessage({
            job: "delete_goal",
            goal_id: data.goal_id,
            workspace_name: data.workspace_name,
          });
        });
      } catch (error) {
        console.error("Error deleting goal:", error);
      }
    },
    [sendMessage]
  );

  const getAnalytics = useCallback(
    async (data: any) => {
      try {
        return new Promise((resolve) => {
          const messageHandler = (event: MessageEvent) => {
            const response = JSON.parse(event.data);

            if (response.job === "get_analytics") {
              console.log("Response from get analytics:", response);
              socket?.removeEventListener("message", messageHandler);
              resolve(response);
            }
          };

          if (socket) {
            socket.addEventListener("message", messageHandler);
          }

          sendMessage({
            job: "get_analytics",
            workspace_name: data.workspace_name,
            social_id: data.social_id,
            start_time: data.start_time,
            end_time: data.end_time,
          });
        });
      } catch (error) {
        console.error("Error getting goals:", error);
      }
    },
    [sendMessage]
  );

  const getProfile = useCallback(
    async (skipLoading = false) => {
      try {
        console.log("csrfToken token:", csrfToken);

        return new Promise<void>((resolve, reject) => {
          const originalOnMessage = socket?.onmessage;

          sendMessage({
            csrf_token: csrfToken,
            job: "profile",
          });

          if (!socket) {
            console.log("Socket not initialized yet");
            return;
          }

          socket.onmessage = async (event) => {
            try {
              const response = JSON.parse(event.data);

              if (response.job == "profile") {
                setUser({
                  email: response?.user?.email || "",
                  firstName: response?.user?.first_name || "",
                  lastName: response?.user?.last_name || "",
                  registerDate: response.user?.register_date || "",
                  plan: response.user?.plan || "",
                  userId: response.user?.id || "",
                  planExpireDate: response.user?.plan_expire_date || "",
                  walletTokens: response.user?.wallet_tokens || "",
                  selectedWorkspace: response.user?.selected_workspace || "",
                  profilePhoto: normalizeUrl(response.user?.profile_photo),
                  // Only set isLoading if not skipping loading state
                  ...(skipLoading ? {} : { isLoading: true }),
                });

                setTimeout(async () => {
                  getWorkspaces().then((res: any) => {
                    if (
                      res?.workspaces?.length &&
                      res.job == "get_all_workspaces"
                    ) {
                      console.log("res of get workspaces:", res);
                      const selectedWorkspace = res.workspaces.find(
                        (workspace: any) =>
                          workspace.workspace_name ===
                          response.user?.selected_workspace
                      );

                      if (selectedWorkspace) {
                        // First update workspace details
                        setUser({
                          selectedWorkspaceDetails: selectedWorkspace,
                          workspaceLogo: selectedWorkspace.logo
                            ? `${process.env.NEXT_PUBLIC_API_URL}${selectedWorkspace.logo}`
                            : "/default-workspace-logo.svg",
                        });

                        // Then handle social accounts if they exist
                        if (selectedWorkspace.social_accounts?.length > 0) {
                          setUser({
                            selectedSocial:
                              selectedWorkspace.social_accounts[
                                selectedWorkspace.social_accounts.length - 1
                              ],
                          });
                        }
                      }
                    }
                  });
                }, 1000);

                setTimeout(() => {
                  getTokenRate();
                }, 500);
                getWalletValue();

                console.log("Profile data updated successfully");
                resolve();
              }
            } catch (error) {
              console.error("Error handling WebSocket response:", error);
              reject(error);
            } finally {
              if (socket) {
                socket.onmessage = originalOnMessage || null;
              }
            }
          };
        });
      } catch (error) {
        console.error("Error in getProfile:", error);
        throw error;
      }
    },
    [csrfToken, sendMessage, getWorkspaces, setUser]
  );
  useEffect(() => {
    if (socket) {
      const messageHandler = (event: MessageEvent) => {
        try {
          const data = JSON.parse(event.data);
          // Handle server-initiated ping if needed
          if (data.type === "ping") {
            socket.send(JSON.stringify({ type: "pong" }));
            return;
          }
          setWebSocketData(data);
        } catch (error) {
          console.error("Error parsing WebSocket message:", error);
        }
      };

      socket.addEventListener("message", messageHandler);

      // Cleanup
      return () => {
        socket.removeEventListener("message", messageHandler);
      };
    }
  }, [socket, setWebSocketData]);

  // Get cache for unread messages
  const getCache = useCallback(
    async (data: { workspace_name: string }) => {
      try {
        return new Promise((resolve, reject) => {
          if (!socket) {
            console.log("Socket not connected");
            reject(new Error("Socket not connected"));
            return;
          }

          const messageHandler = (event: MessageEvent) => {
            try {
              const response = JSON.parse(event.data);
              if (response.job === "get_cache") {
                console.log("Get cache response:", response);
                socket.removeEventListener("message", messageHandler);
                resolve(response);
              }
            } catch (error) {
              socket.removeEventListener("message", messageHandler);
              reject(error);
            }
          };

          socket.addEventListener("message", messageHandler);

          sendMessage({
            job: "get_cache",
            workspace_name: data.workspace_name,
          });
        });
      } catch (error) {
        console.error("Error fetching cache:", error);
        throw error;
      }
    },
    [sendMessage, socket]
  );

  // Mark messages as seen
  const seenCache = useCallback(
    async (data: {
      workspace_name: string;
      cache_type: string;
      platform?: string;
      object_id: string;
    }) => {
      try {
        return new Promise((resolve, reject) => {
          if (!socket) {
            console.log("Socket not connected");
            reject(new Error("Socket not connected"));
            return;
          }

          const messageHandler = (event: MessageEvent) => {
            try {
              const response = JSON.parse(event.data);
              if (response.job === "seen_cache") {
                console.log("Seen cache response:", response);
                socket.removeEventListener("message", messageHandler);
                resolve(response);
              }
            } catch (error) {
              socket.removeEventListener("message", messageHandler);
              reject(error);
            }
          };

          socket.addEventListener("message", messageHandler);

          // Use instagram as the default platform unless facebook is explicitly provided
          const platform =
            data.platform === "facebook" ? "facebook" : "instagram";

          console.log(`Using platform ${platform} for seen_cache`);

          sendMessage({
            job: "seen_cache",
            workspace_name: data.workspace_name,
            cache_type: data.cache_type,
            platform: platform,
            object_id: data.object_id,
          });
        });
      } catch (error) {
        console.error("Error marking cache as seen:", error);
        throw error;
      }
    },
    [sendMessage, socket]
  );

  return {
    initializeWebSocket,
    getAnalytics,
    toggleDeleteAccess,
    createGoal,
    getGoals,
    updateGoal,
    deleteGoal,
    getAutoAnswer,
    editAutoAnswer,
    deleteAutoAnswer,
    enableAutoAnswers,
    disableAutoAnswers,
    toggleAddAccess,
    getEditAccess,
    deleteSavedReply,
    sendMessage,
    getAllSavedReply,
    createSavedReply,
    editTitleSavedReply,
    editTextSavedReply,
    acceptInvite,
    postComment,
    editAccess,
    deleteAccess,
    sendDirectMessage,
    replyToComment,
    deleteComment,
    getDirectMessages,
    getLogs,
    getComments,
    getPostComments,
    getDirect,
    deletePost,
    getTokenRate,
    getWebSocketData,
    sendChangePassword,
    sendSelectedWorkspace,
    getProfile,
    createSchedule,
    updateProfilews,
    getWorkspaceData,
    getWalletValue,
    getWorkspaces,
    changeName,
    createWorkspace,
    getWorkspaceMember,
    deleteWorkspace,
    updateWorkspace,
    addWorkspaceMember,
    generateVideo,
    generateCaption,
    generateHashtags,
    generateImage,
    getPosts,
    editPost,
    createPost,
    deleteWorkspaceMember,
    getDashboard,
    saveDashboard,
    sendReact,
    getCache,
    seenCache,
    socket,
  };
}
