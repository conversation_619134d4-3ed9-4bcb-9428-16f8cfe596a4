import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface Post {
  id: string;
  caption: string;
  publish_date?: string;
  typ3?: string;
  media?: string | Record<string, string>;
  social_media: Array<{
    platform: string;
    social_id: string;
  }>;
  ai_generated?: boolean;
}

interface Workspace {
  length: number;
  id: string;
  workspace_name: string;
  logo: string;
  social_media_count: string | number;
  workspace_members: string;
  created_at: string;
  last_activity: string;
  industry: string;
  website: string;
  members?: string[];
}

interface UserState {
  email: string;
  firstName: string;
  lastName: string;
  registerDate: string;
  plan: string;
  generatedCaption: string;
  goals: [];
  selectedGoal: {};
  planExpireDate: string;
  walletTokens: string;
  profilePhoto: string;
  getWorkspaceDone: boolean;
  walletValue: string;
  status: string;
  accessToken: string;
  refreshToken: string;
  isLoading: boolean;
  initialLoad: boolean;
  workspaces: string[];
  workspacesDetails: string[];
  workspaceLogo: string;
  selectedWorkspace: string;
  userId: string;
  published: Post[];
  selectedWorkspaceDetails: Workspace;
  selectedSocial: {
    platform: string,
    social_id: string,
    social_name: string,
    username: string,
    profile_photo?: string
  },
  videoLoading: boolean;
  imageLoading: boolean;
  activePage: string;
  countDown: number;
  drafted: Post[];
  tokenRate: string;
  scheduled: Post[];
  isGenerating: boolean;
  generationProgress: number;
  estimatedTime: number;
  keywords: string[];
  inputValue: string;
  isAdvancedMode: boolean;
  advancedPrompt: string;
  imageKeywords: string[];
  isAdvancedModeRef: boolean;
  videoKeywords: string[];
  captionKeywords: string[];
  hashtagKeywords: string[];
  hashtags: string[];
  caption: string;
  generatedHashtags: string[];
  inboxLoading: boolean;
  countdown: number;
  progress: number;
  csrfToken: string;
  newMemberAdded: boolean;
  autoAnswers: any[];
  setAutoAnswers: (answers: any[]) => void;
  setUser: (user: Partial<UserState>) => void;
  clearUser: () => void;
}

export const useUserStore = create<UserState>()(
  persist(
    (set) => ({
      email: '',
      firstName: '',
      lastName: '',
      registerDate: '',
      plan: '',

      planExpireDate: '',
      walletTokens: '',
      profilePhoto: '',
      walletValue: '',
      countdown: 0,
      progress: 0,
      csrfToken: '',
      published: [],
      status: '',
      accessToken: '',
      refreshToken: '',
      invoiceHistory: [],
      userId: '',
      isLoading: true,
      isAdvancedModeRef: false,
      getWorkspaceDone: false,
      workspaces: [],
      workspacesDetails: [],
      initialLoad: true,
      selectedWorkspace: '',
      selectedWorkspaceDetails: [],
      selectedSocial: '',
      generatedCaption: '',
      goals: [],
      selectedGoal: {},
      generatedHashtags: [],
      workspaceLogo: '',
      videoLoading: false,
      imageLoading: false,
      activePage: 'Image',
      countDown: 0,
      tokenRate: '',
      inboxLoading: false,
      drafted: [],
      scheduled: [],
      isGenerating: false,
      generationProgress: 0,
      estimatedTime: 0,
      keywords: [],
      inputValue: '',
      isAdvancedMode: false,
      advancedPrompt: '',
      imageKeywords: [],
      videoKeywords: [],
      captionKeywords: [],
      hashtagKeywords: [],
      newMemberAdded: false,
      hashtags: [],
      caption: '',
      autoAnswers: [],
      setAutoAnswers: (answers) => set({ autoAnswers: answers }),
      setUser: (user: Partial<UserState>) => {
        const { setUser: _, clearUser: __, ...updatedState } = user;
        set((state) => ({
          ...state,
          ...updatedState
        }));
      },
      clearUser: () => set({
        email: '',
        firstName: '',
        lastName: '',
        registerDate: '',
        generatedCaption: '',
        plan: '',
        planExpireDate: '',
        walletTokens: '',
        analytics: [],
        profilePhoto: '',
        walletValue: '',
        status: '',
        accessToken: '',
        goals: [],
        initialLoad: true,
        selectedGoal: null,
        getWorkspaceDone: false,
        refreshToken: '',
        isLoading: true,
        workspacesDetails: [],
        workspaces: [],
        userId: '',
        csrfToken: '',
        selectedWorkspaceDetails: [],
        selectedWorkspace: '',
        workspaceLogo: '',
        selectedSocial: '',
        keywords: [],
        videoLoading: false,
        tokenRate: '',
        imageLoading: false,
        inboxLoading: false,
        activePage: 'Image',
        generatedHashtags: [],
        isAdvancedModeRef: false,
        countDown: 0,
        drafted: [],
        scheduled: [],
        isGenerating: false,
        generationProgress: 0,
        estimatedTime: 0,
        keywords: [],
        published: [],
        inputValue: '',
        isAdvancedMode: false,
        advancedPrompt: '',
        imageKeywords: [],
        videoKeywords: [],
        captionKeywords: [],
        hashtagKeywords: [],
        newMemberAdded: false,
        hashtags: [],
        caption: ''
      }),
    }),
    {
      name: 'user-storage',
      storage: {
        getItem: (key) => {
          if (typeof window !== 'undefined') {
            const value = sessionStorage.getItem(key);
            return value ? JSON.parse(value) : null;
          }
          return null;
        },
        setItem: (key, value) => {
          if (typeof window !== 'undefined') {
            sessionStorage.setItem(key, JSON.stringify(value));
          }
        },
        removeItem: (key) => {
          if (typeof window !== 'undefined') {
            sessionStorage.removeItem(key);
          }
        },
      },
    }
  )
);