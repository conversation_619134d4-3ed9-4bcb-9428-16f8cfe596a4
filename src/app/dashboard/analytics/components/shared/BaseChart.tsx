"use client";

import { ReactNode, memo, useEffect, useState } from "react";
import { getPlatformColors } from "../../utils/colors";

// Chart loading skeleton component
const ChartSkeleton = () => {
  return (
    <div className="w-full h-full flex flex-col">
      {/* Chart area skeleton */}
      <div className="flex-1 bg-gray-200 rounded-lg relative overflow-hidden">
        {/* Shimmer effect using existing Tailwind animation */}
        <div className="absolute inset-0 -translate-x-full bg-gradient-to-r from-transparent via-white/50 to-transparent animate-shimmer" />

        {/* Mock chart elements with pulse animation */}
        <div className="absolute bottom-4 left-4 right-4 flex items-end justify-between space-x-2">
          {Array.from({ length: 6 }).map((_, i) => (
            <div
              key={i}
              className="bg-gray-300 rounded-t animate-pulse"
              style={{
                height: `${Math.random() * 60 + 20}%`,
                width: '12%',
                animationDelay: `${i * 0.1}s`,
              }}
            />
          ))}
        </div>

        {/* Mock axis lines */}
        <div className="absolute bottom-4 left-4 right-4 h-px bg-gray-300" />
        <div className="absolute bottom-4 left-4 top-4 w-px bg-gray-300" />
      </div>

      {/* Legend skeleton */}
      <div className="flex justify-center mt-3 space-x-4">
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-gray-300 rounded-full animate-pulse" />
          <div className="w-16 h-3 bg-gray-300 rounded animate-pulse" />
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-gray-300 rounded-full animate-pulse" />
          <div className="w-20 h-3 bg-gray-300 rounded animate-pulse" />
        </div>
      </div>
    </div>
  );
};

interface BaseChartProps {
  title: string;
  icon?: string; // Made optional since we're removing icons
  description?: string;
  value?: string | number;
  change?: {
    value: number;
    isPositive: boolean;
  };
  children: ReactNode;
  actions?: ReactNode;
  className?: string;
  dynamicHeight?: boolean;
  minHeight?: string;
  height?: string;
  mobileHeight?: string; // Height to use on small screens (<768px)
  contentOffset?: number; // Space reserved for header/actions (in px)
  bottomPadding?: number; // Extra space below the chart area (in px)
  isLoading?: boolean; // Show loading skeleton instead of children
  selectedSocial?: {
    platform: string;
    username?: string;
  };
}

export const BaseChart = memo(
  ({
    title,
    icon,
    description,
    value,
    change,
    children,
    actions,
    className = "",
    dynamicHeight = false,
    minHeight = "200px",
    height = "450px",
    mobileHeight,
    contentOffset = 80,
    bottomPadding = 0,
    isLoading = false,
    selectedSocial,
  }: BaseChartProps) => {
    // Get platform-specific colors
    const platformColors = getPlatformColors(selectedSocial?.platform);

    // Use CSS variable + media query to set mobile height instead of JS-driven breakpoints.
    // This avoids mismatches between server and client rendering.
    // Normalize height values to valid CSS (append px when missing)
    const normalizeHeight = (val?: string) => {
      if (!val) return undefined;
      // If already contains a unit, return as is
      if (/\d(px|%|vh|vw|rem|em)$/i.test(val.trim())) return val;
      // If purely numeric string, append px
      if (/^\d+(?:\.\d+)?$/.test(val.trim())) return `${val.trim()}px`;
      return val;
    };

    const containerHeight = normalizeHeight(height) || "450px";
    const mobileContainerHeight = normalizeHeight(mobileHeight) || undefined;

    // Get social media icon based on platform
    const getSocialIcon = (platform: string) => {
      switch (platform?.toLowerCase()) {
        case "instagram":
          return "fab fa-instagram";
        case "facebook":
          return "fab fa-facebook";
        case "twitter":
          return "fab fa-twitter";
        case "linkedin":
          return "fab fa-linkedin";
        case "youtube":
          return "fab fa-youtube";
        case "tiktok":
          return "fab fa-tiktok";
        default:
          return "fas fa-chart-line";
      }
    };

    const extraClass = (className || "").trim();
    const qualifier = extraClass ? `.${extraClass.split(" ").join(".")}` : "";

    return (
      <div
        className={`bg-white rounded-xl p-3 md:p-4 shadow-surface overflow-hidden bi-chart ${extraClass}`}
        style={{
          // Default desktop height; mobile override handled by media query below
          height: containerHeight,
          // expose CSS variable for potential descendant use
          ["--chart-height" as any]: containerHeight,
        }}
      >
        {/* Inline style tag to override height on small screens using mobileHeight when provided */}
        {mobileContainerHeight && (
          <style>{`
            @media (max-width: 767px) {
              .bi-chart${qualifier} { height: ${mobileContainerHeight} !important; }
            }
          `}</style>
        )}
        {/* Header */}
        <div className="flex items-center justify-between mb-4 gap-2 flex-wrap">
          <div className="flex items-center gap-3">
            {/* Social Media Platform Icon */}
            {selectedSocial?.platform && (
              <div className="w-12 h-12 flex items-center justify-center">
                <i
                  className={`${getSocialIcon(selectedSocial.platform)} ${
                    platformColors.iconColor
                  } text-2xl`}
                ></i>
              </div>
            )}

            {/* Title Only */}
            <div className="min-w-0 flex-1">
              <h3 className="text-base md:text-lg font-semibold text-gray-900 truncate">
                {title}
              </h3>
            </div>
          </div>

          {/* Actions */}
          {actions && (
            <div className="shrink-0 w-full sm:w-auto overflow-x-auto whitespace-nowrap pb-1">
              {actions}
            </div>
          )}
        </div>

        {/* Chart Content */}
        <div
          className="w-full"
          style={{
            height: `calc(${containerHeight} - ${contentOffset}px - ${bottomPadding}px)`, // Reserve extra bottom space without shrinking the outer container
            minHeight: 120,
          }}
        >
          {isLoading ? <ChartSkeleton /> : children}
        </div>
        {/* Extra bottom padding to keep axis labels within container */}
        {bottomPadding > 0 && <div style={{ height: `${bottomPadding}px` }} />}
      </div>
    );
  }
);
