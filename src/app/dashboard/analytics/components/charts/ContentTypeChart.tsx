"use client";

import { useMemo } from "react";
import { BaseChart } from "../shared/BaseChart";
import { CHART_COLORS } from "../../utils/colors";

interface ContentTypeChartProps {
  data?: any[];
  timeRange?: "7d" | "30d";
  selectedSocial?: any;
  height?: string;
  listMaxHeight?: string;
}

export const ContentTypeChart = ({
  data,
  timeRange,
  selectedSocial,
  height = "320px",
  listMaxHeight = "220px",
}: ContentTypeChartProps) => {
  const chartData = useMemo(() => {
    if (Array.isArray(data) && data.length > 0) {
      return data.map((item, index) => ({
        name: item.type || item.name || `Type ${index + 1}`,
        value: item.reach || item.value || 0,
      }));
    }
    return [];
  }, [data]);

  const topContentType = useMemo(() => {
    const maxValue = Math.max(...chartData.map((item) => item.value));
    const top = chartData.find((item) => item.value === maxValue);
    return top?.name || "N/A";
  }, [chartData]);

  return (
    <BaseChart
      title="Account Reach / Content Type"
      description={`Top performing: ${topContentType}`}
      value={topContentType}
      selectedSocial={selectedSocial}
      height={height}
      minHeight="320px"
    >
      {chartData.length === 0 ? (
        <div className="flex items-center justify-center h-full min-h-[200px]">
          <div className="text-center">
            <p className="text-gray-500 text-lg mb-2">No data available</p>
            <p className="text-gray-400 text-sm">Content type performance will appear here when available</p>
          </div>
        </div>
      ) : (
        <div
          className="space-y-2 p-2 overflow-y-auto pr-2"
          style={{ maxHeight: listMaxHeight }}
        >
          {chartData.map((item, index) => (
            <div key={index} className="bg-white rounded-lg px-1 py-1">
              <div className="flex justify-between items-center mb-1">
                <span className="text-sm font-medium">{item.name}</span>
                <span className="text-sm text-gray-600">
                  {item.value.toLocaleString()}
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  className="h-2.5 rounded-full"
                  style={{
                    width: `${
                      (item.value / Math.max(...chartData.map((d) => d.value))) *
                      100
                    }%`,
                    backgroundColor: CHART_COLORS.instagram,
                  }}
                ></div>
              </div>
            </div>
          ))}
        </div>
      )}
    </BaseChart>
  );
};
