"use client";

import { useEffect, useMemo, useState } from "react";
import { <PERSON><PERSON><PERSON>, Pie, ResponsiveContainer, Cell, Tooltip, Sector } from "recharts";
import { BaseChart } from "../shared/BaseChart";
import { CHART_COLORS, getPlatformColors } from "../../utils/colors";

interface FollowerViewsChartProps {
  data?: any[];
  timeRange: "7d" | "30d";
  selectedSocial?: {
    platform: string;
    username?: string;
  };
  height?: string;
}


export const FollowerViewsChart = ({
  data,
  timeRange,
  selectedSocial,
  height = "450px",
}: FollowerViewsChartProps) => {
  const platformColors = getPlatformColors(selectedSocial?.platform);

  // Detect mobile viewport
  const [isMobile, setIsMobile] = useState(false);
  useEffect(() => {
    const mq = window.matchMedia("(max-width: 640px)");
    const update = () => setIsMobile(mq.matches);
    update();
    mq.addEventListener ? mq.addEventListener("change", update) : mq.addListener(update);
    return () => {
      mq.removeEventListener ? mq.removeEventListener("change", update) : mq.removeListener(update);
    };
  }, []);

  // Active slice state for hover interaction
  const [activeIndex, setActiveIndex] = useState<number | undefined>(undefined);

  // Responsive pie sizes (use percentage so it scales with container)
  const innerR = isMobile ? "45%" : "50%";
  const outerR = isMobile ? "65%" : "70%";

  // Custom label component for external percentage labels (responsive)
  const renderCustomizedLabel = ({
    cx,
    cy,
    midAngle,
    outerRadius,
    percent,
  }: any) => {
    const RADIAN = Math.PI / 180;
    const radiusOffset = isMobile ? 16 : 18; // reduced offset on desktop to avoid clipping
    const radius = outerRadius + radiusOffset; // Position labels outside the chart
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text
        x={x}
        y={y}
        fill="#374151"
        textAnchor={x > cx ? "start" : "end"}
        dominantBaseline="central"
        fontSize={isMobile ? 12 : 16}
        fontWeight="600"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  // Compute follower vs non-follower from provided data; no demo fallback
  const chartData = useMemo(() => {
    let followerViews = 0;
    let nonFollowerViews = 0;

    if (Array.isArray(data) && data.length > 0) {
      data.forEach((dp: any) => {
        const type = dp?.breakdown?.follower_type || dp?.breakdown?.follow_type || dp?.follower_type;
        const val = Number(dp?.total_value ?? dp?.value ?? 0) || 0;
        if (typeof type === "string") {
          const t = type.toLowerCase();
          if (t.includes("follower") && !t.includes("non")) followerViews += val;
          else if (t.includes("non_follower") || t.includes("non-follower") || t.startsWith("non")) nonFollowerViews += val;
        }
      });
    }

    return [
      {
        name: "follower",
        value: followerViews,
        fill: platformColors.primary, // Platform-specific color for followers
      },
      {
        name: "non-follower",
        value: nonFollowerViews,
        fill: platformColors.primaryOpacity, // Platform-specific low opacity color for non-followers
      },
    ];
  }, [data, platformColors]);

  const totalViews = useMemo(() => {
    return chartData.reduce((sum, item) => sum + item.value, 0);
  }, [chartData]);

  // Custom active shape: reliable subtle expansion on hover
  const renderActiveShape = (props: any) => {
    const { cx, cy, innerRadius, outerRadius, startAngle, endAngle, fill } = props;
    return (
      <g>
        <Sector
          cx={cx}
          cy={cy}
          innerRadius={innerRadius}
          outerRadius={outerRadius + 8}
          startAngle={startAngle}
          endAngle={endAngle}
          fill={fill}
        />
      </g>
    );
  };

  // Custom tooltip content
  const CustomTooltip = ({ active, payload }: any) => {
    if (!active || !payload || !payload.length) return null;
    const item = payload[0];
    const name = item?.name || item?.payload?.name;
    const value: number = Number(item?.value || 0);
    const percent = totalViews ? Math.round((value / totalViews) * 100) : 0;
    const color = item?.payload?.fill || platformColors.primary;
    return (
      <div className="rounded-lg shadow-lg border border-gray-200 bg-white px-3 py-2">
        <div className="flex items-center gap-2">
          <span className="inline-block w-2.5 h-2.5 rounded-full" style={{ backgroundColor: color }} />
          <span className="text-sm font-medium capitalize text-gray-800">{name}</span>
        </div>
        <div className="mt-1 text-xs text-gray-600">
          <span className="font-semibold text-gray-900">{value.toLocaleString()}</span>
          <span className="ml-2">({percent}%)</span>
        </div>
      </div>
    );
  };

  const hasData = useMemo(() => {
    const total = chartData.reduce((sum, item) => sum + (Number(item.value) || 0), 0);
    return total > 0;
  }, [chartData]);

  return (
    <BaseChart
      title="Follower vs Non-Follower Views"
      selectedSocial={selectedSocial}
      height={height}
      contentOffset={80}
    >
      {!hasData ? (
        <div className="flex items-center justify-center h-full min-h-[200px]">
          <div className="text-center">
            <p className="text-gray-500 text-lg mb-2">No data available</p>
            <p className="text-gray-400 text-sm">Follower vs Non-Follower views will appear here when available</p>
          </div>
        </div>
      ) : (
      <div className="h-full flex flex-col justify-center items-center pb-6 sm:pb-8">
        {/* Pie Chart */}
        <div className="flex-1 min-h-0 w-full relative">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Tooltip content={<CustomTooltip />} cursor={false} wrapperStyle={{ outline: "none", zIndex: 20 }} />
              <Pie
                data={chartData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={renderCustomizedLabel}
                innerRadius={innerR}
                outerRadius={outerR}
                fill="#8884d8"
                dataKey="value"
                startAngle={90}
                endAngle={450}
                isAnimationActive={true}
                animationBegin={120}
                animationDuration={900}
                animationEasing="ease-in-out"
                activeIndex={activeIndex}
                activeShape={renderActiveShape}
                onMouseEnter={(_, index) => setActiveIndex(index)}
                onMouseMove={(_, index) => setActiveIndex(index)}
                onMouseLeave={() => setActiveIndex(undefined)}
              >
                {chartData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.fill} />
                ))}
              </Pie>
            </PieChart>
          </ResponsiveContainer>
          {/* Center total */}
          <div className="pointer-events-none absolute inset-0 flex items-center justify-center z-0">
            <div className="text-center">
              <div className="text-xl sm:text-2xl font-bold text-gray-800">
                {totalViews.toLocaleString()}
              </div>
              <div className="text-xs sm:text-sm text-gray-500">Total Views</div>
            </div>
          </div>
        </div>

        {/* Legend */}
        <div className="shrink-0 mt-3 sm:mt-4">
          <div className="flex items-center justify-center gap-5 sm:gap-6">
            <div className="flex items-center gap-2">
              <div
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: platformColors.primaryOpacity }}
              ></div>
              <span className="text-xs sm:text-sm text-gray-700 font-medium">non-follower</span>
            </div>
            <div className="flex items-center gap-2">
              <div
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: platformColors.primary }}
              ></div>
              <span className="text-xs sm:text-sm text-gray-700 font-medium">follower</span>
            </div>
          </div>
        </div>
      </div>
      )}
    </BaseChart>
  );
};
