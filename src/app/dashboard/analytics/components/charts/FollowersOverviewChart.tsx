"use client";

import { useMemo, useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  Line,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON>s,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
import { BaseChart } from "../shared/BaseChart";
import { CHART_COLORS, getPlatformColors } from "../../utils/colors";

interface FollowersOverviewChartProps {
  data?: any[];
  timeRange: "7d" | "30d";
  selectedSocial?: {
    platform: string;
    username?: string;
  };
  height?: string;
  isLoading?: boolean;
}

export const FollowersOverviewChart = ({
  data,
  timeRange,
  selectedSocial,
  height = "450px",
}: FollowersOverviewChartProps) => {
  const [viewMode, setViewMode] = useState<"monthly" | "weekly">("monthly");
  const platformColors = getPlatformColors(selectedSocial?.platform);

  // Custom tooltip component
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 rounded-lg shadow-lg border border-gray-200">
          <p className="text-sm font-medium text-gray-900">{label}</p>
          <p className="text-sm" style={{ color: platformColors.primary }}>
            <span
              className="inline-block w-3 h-3 rounded-full mr-2"
              style={{ backgroundColor: platformColors.primary }}
            ></span>
            Followers: {payload[0].value?.toLocaleString()}
          </p>
        </div>
      );
    }
    return null;
  };

  // Build chart data from real follower_count series
  const chartData = useMemo(() => {
    const series = Array.isArray(data) ? data : [];

    // Normalize points: use dp.date (preferred) or end_time, followers as total_value/value
    const normalized = series
      .map((dp: any) => {
        const dateStr = dp?.date || dp?.end_time;
        const val = Number(dp?.total_value ?? dp?.value ?? 0) || 0;
        const d = dateStr ? new Date(dateStr) : undefined;
        // Format label as e.g. "Aug 05"
        const period = d
          ? d.toLocaleDateString(undefined, { month: "short", day: "2-digit" })
          : dateStr || "";
        return {
          period,
          followers: val,
          fullDate: dateStr || period,
          _ts: d ? d.getTime() : 0,
        };
      })
      // guard invalid dates to end
      .sort((a: any, b: any) => a._ts - b._ts);

    const windowSize = viewMode === "weekly" ? 7 : 30;
    const sliced = normalized.slice(-windowSize);
    return sliced.length ? sliced : normalized;
  }, [data, viewMode]);

  const totalFollowers = useMemo(() => {
    return chartData.reduce((sum, item) => sum + item.followers, 0);
  }, [chartData]);

  // View mode toggle actions
  const actions = (
    <div className="flex bg-gray-100 rounded-lg p-1">
      <button
        onClick={() => setViewMode("monthly")}
        className={`px-3 py-1 rounded-md text-sm font-medium transition-all ${
          viewMode === "monthly"
            ? "bg-white shadow-sm"
            : "text-gray-600 hover:text-gray-900"
        }`}
        style={{
          color: viewMode === "monthly" ? platformColors.primary : undefined,
        }}
      >
        Last Month
      </button>
      <button
        onClick={() => setViewMode("weekly")}
        className={`px-3 py-1 rounded-md text-sm font-medium transition-all ${
          viewMode === "weekly"
            ? "bg-white shadow-sm"
            : "text-gray-600 hover:text-gray-900"
        }`}
        style={{
          color: viewMode === "weekly" ? platformColors.primary : undefined,
        }}
      >
        Last Week
      </button>
    </div>
  );

  return (
    <BaseChart
      title="Overview of followers"
      selectedSocial={selectedSocial}
      actions={actions}
      height={height}
      contentOffset={110}
    >
      {chartData.length === 0 ? (
        <div className="flex items-center justify-center h-full min-h-[200px]">
          <div className="text-center">
            <p className="text-gray-500 text-lg mb-2">No data available</p>
            <p className="text-gray-400 text-sm">
              Followers overview data will appear here when available
            </p>
          </div>
        </div>
      ) : (
        <ResponsiveContainer width="100%" height="100%">
          <LineChart
            data={chartData}
            margin={{ top: 20, right: 24, left: 12, bottom: 40 }}
          >
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis
              dataKey="period"
              tick={{ fontSize: 10 }}
              interval="preserveStartEnd"
              angle={-30}
              dy={8}
              tickMargin={8}
              minTickGap={6}
              padding={{ left: 6, right: 6 }}
            />
            <YAxis
              tick={{ fontSize: 12 }}
              tickFormatter={(value: number) => `${value.toLocaleString()}`}
              domain={["auto", "auto"]}
              allowDecimals={false}
            />
            <Tooltip content={<CustomTooltip />} />
            <Line
              type="monotone"
              dataKey="followers"
              stroke={platformColors.primary}
              strokeWidth={3}
              dot={{ fill: platformColors.primary, strokeWidth: 2, r: 4 }}
              activeDot={{
                r: 6,
                stroke: platformColors.primary,
                strokeWidth: 2,
              }}
            />
          </LineChart>
        </ResponsiveContainer>
      )}
    </BaseChart>
  );
};
