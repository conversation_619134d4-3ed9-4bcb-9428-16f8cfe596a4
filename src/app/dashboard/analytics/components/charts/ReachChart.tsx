"use client";

import { useMemo, useState, useEffect } from "react";
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
} from "recharts";
import { BaseChart } from "../shared/BaseChart";
import { getPlatformColors } from "../../utils/colors";

interface ReachChartProps {
  data: any[];
  timeRange: "7d" | "30d";
  selectedSocial?: {
    platform: string;
    username?: string;
  };
  height?: string;
  mobileHeight?: string;
  bottomPadding?: number;
  isLoading?: boolean;
}

export const ReachChart = ({
  data,
  timeRange,
  selectedSocial,
  height,
  mobileHeight,
  bottomPadding = 0,
  isLoading = false,
}: ReachChartProps) => {
  const [viewMode, setViewMode] = useState<"timeline" | "breakdown">(
    "timeline"
  );
  const [isMobile, setIsMobile] = useState(false);
  const platformColors = getPlatformColors(selectedSocial?.platform);

  useEffect(() => {
    const check = () =>
      setIsMobile(typeof window !== "undefined" && window.innerWidth <= 640);
    check();
    window.addEventListener("resize", check);
    return () => window.removeEventListener("resize", check);
  }, []);

  const parsePx = (val?: string) => {
    if (!val) return undefined;
    const n = parseInt(val as any, 10);
    return Number.isFinite(n) ? n : undefined;
  };

  // Aggressive internal height limiting: constrain chart area regardless of parent
  const MAX_DESKTOP_CAP = 360; // px
  const DEFAULT_MOBILE_HEIGHT = 300; // px
  const DEFAULT_DESKTOP_HEIGHT = 320; // px
  const maxHeight = isMobile
    ? Math.min(parsePx(mobileHeight) ?? DEFAULT_MOBILE_HEIGHT, DEFAULT_MOBILE_HEIGHT)
    : Math.min(parsePx(height) ?? DEFAULT_DESKTOP_HEIGHT, MAX_DESKTOP_CAP);
  const minHeight = isMobile ? 200 : 220;

  const chartData = useMemo(() => {
    const effectiveData = data && data.length > 0 ? data : [];
    if (effectiveData.length === 0) return [];

    const days = timeRange === "7d" ? 7 : 30;
    const toYMD = (d: string) => (d || "").split("T")[0];
    const monthDay = (ymd: string) => {
      const [y, m, dd] = ymd.split("-").map((s) => parseInt(s, 10));
      if (!y || !m || !dd) return ymd;
      return new Date(y, m - 1, dd).toLocaleDateString(undefined, {
        month: "short",
        day: "2-digit",
      });
    };

    const byDate = new Map<string, number>();
    effectiveData.forEach((item: any) => {
      const dateStr = toYMD(String(item?.date || item?.end_time || ""));
      if (!dateStr) return;
      const val = Number(item?.total_value || item?.value || 0) || 0;
      byDate.set(dateStr, (byDate.get(dateStr) || 0) + val);
    });
    const sortedDates = Array.from(byDate.keys()).sort();
    const lastNDates = sortedDates.slice(-days);
    return lastNDates.map((date) => ({
      date,
      dateLabel: monthDay(date),
      reach: byDate.get(date) || 0,
    }));
  }, [data, timeRange]);

  // compute weekly ticks (every 7 days) from chartData
  const xAxisTicks = useMemo(() => {
    if (!chartData || chartData.length === 0) return [];
    // chartData is ordered by date (ascending). We'll pick first, then every 7th.
    const ticks = chartData
      .map((d) => d.date)
      .filter((_, idx) => idx % 7 === 0 || idx === chartData.length - 1);

    // If the last two ticks are adjacent (<= 1 day apart), drop the penultimate one
    if (ticks.length >= 2) {
      const toDate = (s: string) => {
        const parts = (s || "").split("-").map((n) => parseInt(n, 10));
        const y = parts[0] || 1970;
        const m = parts[1] || 1;
        const d = parts[2] || 1;
        return new Date(y, m - 1, d);
      };
      const lastStr = ticks[ticks.length - 1];
      const prevStr = ticks[ticks.length - 2];
      if (lastStr && prevStr) {
        const last = toDate(lastStr);
        const prev = toDate(prevStr);
        const diffDays = Math.round(
          (last.getTime() - prev.getTime()) / (1000 * 60 * 60 * 24)
        );
        if (diffDays <= 1) {
          return ticks.slice(0, ticks.length - 2).concat([lastStr]);
        }
      }
    }

    return ticks;
  }, [chartData]);

  const breakdownData = useMemo(() => {
    const effectiveData = data && data.length > 0 ? data : [];
    if (effectiveData.length === 0) return [];

    const breakdown = effectiveData.reduce((acc: any, item) => {
      const type = item.breakdown?.media_product_type || "post";
      acc[type] = (acc[type] || 0) + (item.total_value || 0);
      return acc;
    }, {});

    return Object.entries(breakdown).map(([type, value]) => ({
      type: type.charAt(0).toUpperCase() + type.slice(1),
      reach: value,
    }));
  }, [data]);

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-800">{label}</p>
          <div className="space-y-1">
            <p className="text-sm">
              <span
                className="font-medium"
                style={{ color: platformColors.primary }}
              >
                Reach: {payload[0]?.value?.toLocaleString()}
              </span>
            </p>
          </div>
        </div>
      );
    }
    return null;
  };

  const actions = (
    <div className="flex bg-gray-100 rounded-lg p-1">
      <button
        onClick={() => setViewMode("timeline")}
        className={`px-3 py-1 rounded-md text-sm font-medium transition-all ${
          viewMode === "timeline"
            ? "bg-white shadow-sm"
            : "text-gray-600 hover:text-gray-900"
        }`}
        style={{
          color: viewMode === "timeline" ? platformColors.primary : undefined,
        }}
      >
        Timeline
      </button>
      <button
        onClick={() => setViewMode("breakdown")}
        className={`px-3 py-1 rounded-md text-sm font-medium transition-all ${
          viewMode === "breakdown"
            ? "bg-white shadow-sm"
            : "text-gray-600 hover:text-gray-900"
        }`}
        style={{
          color: viewMode === "breakdown" ? platformColors.primary : undefined,
        }}
      >
        By Type
      </button>
    </div>
  );

  // Keep height consistent with other charts by default; only use a mobile override when explicitly provided by caller
  const effectiveHeight = height || "320px";

  return (
    <BaseChart
      title="Reach"
      selectedSocial={selectedSocial}
      actions={actions}
      className="reach-chart"
      height={effectiveHeight}
      mobileHeight={mobileHeight}
      bottomPadding={bottomPadding}
      contentOffset={100}
      isLoading={isLoading}
    >
      {chartData.length === 0 ? (
        <div className="flex items-center justify-center h-full min-h-[200px]">
          <div className="text-center">
            <p className="text-gray-500 text-lg mb-2">No data available</p>
            <p className="text-gray-400 text-sm">
              Reach data will appear here when available
            </p>
          </div>
        </div>
      ) : (
        <>
          {/* Use a numeric px height only on mobile so Recharts reserves space for XAxis ticks; on desktop keep responsive 100% to avoid overflowing parent */}
          <div
            style={{ height: "100%", maxHeight, minHeight, overflow: "hidden" }}
          >
            <ResponsiveContainer width="100%" height="100%">
              {viewMode === "timeline" ? (
                <AreaChart
                  data={chartData}
                  margin={{
                    top: 28,
                    right: isMobile ? 32 : 56,
                    left: 16,
                    bottom: 16,
                  }}
                >
                <defs>
                  <linearGradient
                    id="reachGradient"
                    x1="0"
                    y1="0"
                    x2="0"
                    y2="1"
                  >
                    <stop
                      offset="5%"
                      stopColor={platformColors.primary}
                      stopOpacity={0.8}
                    />
                    <stop
                      offset="95%"
                      stopColor={platformColors.primary}
                      stopOpacity={0.1}
                    />
                  </linearGradient>
                </defs>
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                <XAxis
                  dataKey="date"
                  ticks={xAxisTicks}
                  tickFormatter={(value: any) => {
                    // find the matching label in chartData
                    const d = chartData.find((c) => c.date === value);
                    if (!d) return value;
                    // add a tiny padding on the last tick to avoid overlap with edge
                    const isLast = xAxisTicks[xAxisTicks.length - 1] === value;
                    return isLast ? `${d.dateLabel}\u00A0` : d.dateLabel;
                  }}
                  tick={{ fontSize: 12, fill: "#666" }}
                  minTickGap={8}
                  tickMargin={8}
                  interval={0}
                  angle={isMobile ? -30 : 0}
                  height={isMobile ? 48 : 36}
                />
                <YAxis
                  tick={{ fontSize: 12 }}
                  tickFormatter={(value) =>
                    value >= 1000 ? `${(value / 1000).toFixed(1)}k` : value
                  }
                  // Add 10% headroom to avoid top clipping within plot area
                  domain={[0, (dataMax: number) => Math.ceil(dataMax * 1.1)]}
                />
                <Tooltip content={<CustomTooltip />} />
                <Area
                  type="monotone"
                  dataKey="reach"
                  stroke={platformColors.primary}
                  strokeWidth={2}
                  fill="url(#reachGradient)"
                />
              </AreaChart>
            ) : (
              <BarChart
                data={breakdownData}
                margin={{
                  top: 28,
                  right: 24,
                  left: 16,
                  bottom: isMobile ? 40 : 16,
                }}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                <XAxis
                  dataKey="type"
                  tick={{ fontSize: 12, fill: "#666" }}
                  tickMargin={8}
                  interval={0}
                  angle={isMobile ? -20 : 0}
                  height={isMobile ? 48 : 36}
                />
                <YAxis
                  tick={{ fontSize: 12 }}
                  tickFormatter={(value) =>
                    value >= 1000 ? `${(value / 1000).toFixed(1)}k` : value
                  }
                />
                <Tooltip
                  formatter={(value) => [
                    `${(value as number)?.toLocaleString()} reach`,
                    "Reach",
                  ]}
                />
                <Bar
                  dataKey="reach"
                  fill={platformColors.primary}
                  radius={[4, 4, 0, 0]}
                />
              </BarChart>
            )}
            </ResponsiveContainer>
          </div>
        </>
      )}
    </BaseChart>
  );
};
