"use client";
import React from "react";
import { motion } from "framer-motion";
import Dashboardbtn from "~/components/dashboardbtn";

const MotionDiv = motion.div;
const MotionButton = motion.button;

interface ChargeTokenCardProps {
  chargeValue: string;
  tokenRate: string;
  onDecrement: () => void;
  onIncrement: () => void;
  onPay: () => void;
  isPayDisabled: boolean;
}

export const ChargeTokenCard: React.FC<ChargeTokenCardProps> = ({
  chargeValue,
  tokenRate,
  onDecrement,
  onIncrement,
  onPay,
  isPayDisabled,
}) => {
  return (
    <MotionDiv
      className="bg-white rounded-lg shadow-md p-3 py-6 flex flex-col justify-between items-center gap-1 h-full mb-4 md:mb-0"
      variants={{ hidden: { opacity: 0, y: 10 }, show: { opacity: 1, y: 0 } }}
      transition={{ type: "spring", stiffness: 250, damping: 24 }}
    >
      <div className="title w-full flex items-center justify-center flex-col">
        <h2 className="text-lg font-bold mb-2">Charge Token</h2>
        <p className="text-gray-600 mb-2 text-center text-sm">
          Choose The Amount You Wanted To Charge
        </p>
      </div>
      <div className="flex p-2 items-center justify-center gap-4 mb-1 bg-[#C6D59F] rounded-md ">
        <span className="text-2xl text-white font-bold">{chargeValue}</span>
        <div className="flex flex-row gap-2">
          <MotionButton
            whileTap={{ scale: 0.98 }}
            className="bg-white text-gray-700 rounded-md w-8 h-8 flex items-center justify-center"
            onClick={onDecrement}
          >
            -
          </MotionButton>
          <MotionButton
            whileTap={{ scale: 0.98 }}
            className="bg-white text-gray-700  rounded-md w-8 h-8 flex items-center justify-center"
            onClick={onIncrement}
          >
            +
          </MotionButton>
        </div>
      </div>
      <p className="text-center text-xl font-bold mb-2">
        {Number(chargeValue) * parseFloat(tokenRate)}$
      </p>
      <Dashboardbtn
        onClick={onPay}
        variant="default"
        className={`w-full md:w-auto ${
          isPayDisabled ? "opacity-50 cursor-not-allowed" : ""
        }`}
        disabled={isPayDisabled}
      >
        Pay
      </Dashboardbtn>
    </MotionDiv>
  );
};

export default ChargeTokenCard;
