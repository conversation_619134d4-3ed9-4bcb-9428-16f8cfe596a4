"use client";
import React from "react";
import { motion } from "framer-motion";

const MotionDiv = motion.div;

export interface InvoiceItem {
  created: number;
  amount?: number | string | null;
  is_paid: boolean;
  description?: string;
}

interface InvoiceHistoryProps {
  invoices: InvoiceItem[];
}

export const InvoiceHistory: React.FC<InvoiceHistoryProps> = ({ invoices }) => {
  return (
    <MotionDiv
      className="bg-white rounded-lg shadow-md p-3 flex flex-col justify-center items-center h-full mb-4 md:mb-0"
      variants={{ hidden: { opacity: 0, y: 10 }, show: { opacity: 1, y: 0 } }}
      transition={{ type: "spring", stiffness: 250, damping: 24 }}
    >
      <h2 className="text-lg font-bold mb-2">Invoice History</h2>
      <div className="overflow-y-auto w-full grow max-h-[150px] md:max-h-[150px]">
        <table className="min-w-full divide-y divide-gray-200">
          <tbody className="bg-white divide-y divide-gray-200">
            {Array.isArray(invoices) && invoices.length > 0 ? (
              invoices.map((invoice, index) => {
                const amountNum =
                  typeof invoice.amount === "number"
                    ? invoice.amount
                    : parseFloat((invoice.amount ?? "").toString());
                const amountDisplay = Number.isFinite(amountNum)
                  ? amountNum.toFixed(2)
                  : "-";
                const createdMs =
                  typeof invoice.created === "number"
                    ? invoice.created * 1000
                    : Date.now();
                return (
                  <tr key={index}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(createdMs).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      ${amountDisplay}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          invoice.is_paid
                            ? "bg-green-100 text-green-800"
                            : "bg-red-100 text-red-800"
                        }`}
                      >
                        {invoice.is_paid ? "Paid" : "Unpaid"}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {invoice.description || ""}
                    </td>
                  </tr>
                );
              })
            ) : (
              <tr>
                <td
                  colSpan={4}
                  className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center"
                >
                  No invoice history available
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </MotionDiv>
  );
};

export default InvoiceHistory;
