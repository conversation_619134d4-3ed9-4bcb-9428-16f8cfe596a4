"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Loadingscreen from "~/components/loadingscreen";
import Dashboardbtn from "../../../components/dashboardbtn";
import { CreatePostBtn } from "~/components/createpostbtn";
import { useWebSocket } from "../../../hooks/useWebSocket";
import { useUserStore } from "~/store/userStore";
import DashboardLayout from "../(components)/DashboardLayout";
import {
  showSuccessToast,
  ToastWrapper,
  showErrorToast,
} from "../../../components/toasts";
import Image from "next/image";
import { motion, AnimatePresence } from "framer-motion";
import ProfileInfo from "./(components)/ProfileInfo";
import SubscriptionCard from "./(components)/SubscriptionCard";
import ChargeTokenCard from "./(components)/ChargeTokenCard";
import InvoiceHistory from "./(components)/InvoiceHistory";
import DeleteAccountModal from "./(components)/DeleteAccountModal";
import PaymentStatusModal from "./(components)/PaymentStatusModal";

const MotionDiv = motion.div;
const MotionButton = motion.button;

interface CSRFResponse {
  csrf: string;
}

interface Invoice {
  created: number;
  // Add other invoice properties as needed
}

interface UserInvoiceHistory {
  results: Invoice[];
}

interface PaymentModalConfig {
  loading: {
    icon: JSX.Element;
    title: string;
    message: string;
    bgColor: string;
    textColor: string;
  };
  success: {
    icon: JSX.Element;
    title: string;
    message: string;
    bgColor: string;
    textColor: string;
  };
  error: {
    icon: JSX.Element;
    title: string;
    message: string;
    bgColor: string;
    textColor: string;
  };
  idle: {
    icon: JSX.Element;
    title: string;
    message: string;
    bgColor: string;
    textColor: string;
  };
}

const Dashboard = () => {
  const {
    initializeWebSocket,
    getWebSocketData,
    sendMessage,
    getProfile,
    updateProfilews,
    sendChangePassword,
    getWalletValue,
    getWorkspaces,
    changeName,
  } = useWebSocket();
  const router = useRouter();
  const { setUser, plan, tokenRate } = useUserStore();
  const searchParams = useSearchParams();
  const [showChangePlanModal, setShowChangePlanModal] = useState(false);
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);

  const [chargeValue, setChargeValue] = useState("0");
  const [isSubscriptionModalOpen, setIsSubscriptionModalOpen] = useState(false);

  const {
    email: userEmail,
    firstName: userFirstName,
    lastName: userLastName,
    registerDate: userRegisterDate,
    plan: userPlan,
    planExpireDate: userPlanExpireDate,
    walletTokens: userWalletTokens,
    profilePhoto: userProfilePhoto,
    walletValue: userWalletValue,
    workspaces: userWorkspaces,
    invoiceHistory: userInvoiceHistory,
  } = useUserStore();

  const [profilePhoto, setProfilePhoto] = useState(userProfilePhoto);

  useEffect(() => {
    console.log("userProfilePhoto", userProfilePhoto);

    setProfilePhoto(userProfilePhoto);
  }, [userProfilePhoto]);

  const [paymentStatus, setPaymentStatus] = useState<
    "idle" | "loading" | "success" | "error"
  >("idle");

  console.log(userInvoiceHistory.results, " this is a test");

  useEffect(() => {
    const checkAuth = () => {
      const status = sessionStorage.getItem("status");
      const accessToken = sessionStorage.getItem("accessToken");
      const refreshToken = sessionStorage.getItem("refreshToken");
      if (status !== "authenticated" || !accessToken || !refreshToken) {
        router.push("/login");
      }
    };

    setTimeout(checkAuth, 3000);
  }, [router]);

  const [isEditingName, setIsEditingName] = useState(false);
  const [newFirstName, setNewFirstName] = useState(userFirstName);
  const [newLastName, setNewLastName] = useState(userLastName);

  const handlePaymentToken = async (amount: number) => {
    const amountStr = amount.toString();
    // const csrfResponse = await fetch(
    //     `${process.env.NEXT_PUBLIC_API_URL}/api/login/`,
    //     {
    //       method: "GET",
    //     },
    //   );
    //   const csrfData = await csrfResponse.json() as CSRFResponse;
    //   const csrfToken: string = csrfData.csrf;
    const csrfToken = useUserStore.getState().csrfToken;

    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}/api/token-payment/`,
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${sessionStorage.getItem("accessToken")}`,
          "Content-Type": "application/json",
          csrf_token: csrfToken,
        },
        body: JSON.stringify({
          token_amount: amountStr,
        }),
      }
    );

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to charge tokens");
    }

    const data = await response.json();
    // showSuccessToast('Tokens charged successfully',  "userprofile-toast");
    router.push(data.url);
    console.log(data);
  };

  const handleSubscribePayment = async (amount: string) => {
    // const csrfResponse = await fetch(
    //     `${process.env.NEXT_PUBLIC_API_URL}/api/login/`,
    //     {
    //       method: "GET",
    //     },
    //   );
    //   const csrfData = await csrfResponse.json() as CSRFResponse;
    //   const csrfToken: string = csrfData.csrf;
    const csrfToken = useUserStore.getState().csrfToken;

    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}/api/token-payment/`,
      {
        method: "PUT",
        headers: {
          Authorization: `Bearer ${sessionStorage.getItem("accessToken")}`,
          "Content-Type": "application/json",
          csrf_token: csrfToken,
        },
      }
    );

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to charge tokens");
    }

    const data = await response.json();
    // showSuccessToast('Tokens charged successfully',  "userprofile-toast");
    router.push(data.url);
    console.log(data);
  };

  const handleNameChange = () => {
    if (isEditingName) {
      if (typeof changeName === "function") {
        changeName({ first_name: newFirstName, last_name: newLastName })
          .then((response) => {
            // Show success toast

            showSuccessToast("Name updated successfully", "userprofile-toast");
            // Refresh the page after 1 second
            // setTimeout(() => {
            //     window.location.reload();
            // }, 1000);
          })
          .catch((error) => {
            // Show error toast
            showErrorToast(
              "Failed to update name, run out of time",
              "userprofile-toast"
            );
            // setTimeout(() => {
            //     window.location.reload();
            // }, 1000);
            console.error("Error updating name:", error);
          });
      } else {
        console.error("changeName function is not available");
        console.error("Unable to change name at this time");
      }
    }
    setIsEditingName(!isEditingName);
  };

  const handleFirstNameChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setNewFirstName(event.target.value);
  };

  const handleLastNameChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setNewLastName(event.target.value);
  };

  const handleChangePassword = () => {
    sendChangePassword().then((response) => {
      if (response.success) {
        showSuccessToast(
          "change password url sent successfully!",
          "userprofile-toast"
        );
      } else {
        showErrorToast(response.message, "userprofile-toast");
      }
    });
  };

  const uploadPicture = async () => {
    const fileInput = document.createElement("input");
    fileInput.type = "file";
    fileInput.accept = "image/jpeg";
    fileInput.click();

    fileInput.onchange = async (event) => {
      const target = event.target as HTMLInputElement;
      if (target.files?.[0]) {
        const file = target.files[0];
        const reader = new FileReader();

        reader.onloadend = async () => {
          const base64String = reader.result as string;
          const base64Image = base64String.split(",")[1];

          try {
            if (typeof updateProfilews === "function") {
              const response = await updateProfilews(base64Image);
              console.log(response.message.photo, " this is a test5");

              // Update profile photos using Next.js Image component
              const profileImgs =
                document.getElementsByClassName("profile-photo");
              // Array.from(profileImgs).forEach((img) => {
              //     if (img instanceof HTMLElement) {
              //         const imgComponent = (
              //             <Image
              //                 src={`https://businessinsight.ai${response.message.photo.value}`}
              //                 alt="Profile"
              //                 width={40}
              //                 height={40}
              //                 className="rounded-full"
              //             />
              //         );
              //         img.replaceWith(imgComponent);
              //     }
              // });

              showSuccessToast(
                "Profile picture updated successfully",
                "userprofile-toast"
              );
            } else {
              throw new Error("Update profile function not available");
            }
          } catch (error) {
            console.error(
              "An error occurred while uploading the image:",
              error
            );
            showErrorToast(
              "Failed to update profile picture",
              "userprofile-toast"
            );
          }
        };

        reader.readAsDataURL(file);
      }
    };
  };

  useEffect(() => {
    if (searchParams.get("modal") === "changePlan") {
      setIsSubscriptionModalOpen(true);
    }
  }, [searchParams]);

  const handleCloseModal = () => {
    setIsSubscriptionModalOpen(false);
    window.history.replaceState({}, "", "/dashboard/user-profile");
  };

  useEffect(() => {
    let paymentId = searchParams.get("payment_id");

    if (paymentId) {
      setPaymentStatus("loading");

      const checkPaymentStatus = async () => {
        try {
          const response = await fetch(
            `${process.env.NEXT_PUBLIC_API_URL}/api/payment/webhook/?payment_id=${paymentId}`
          );
          const data = await response.json();

          if (data.status.payment_status === "paid") {
            setPaymentStatus("success");

            // Optionally refresh user data here
          } else {
            setPaymentStatus("error");
          }
        } catch (error) {
          console.error("Error checking payment status:", error);
          setPaymentStatus("error");
          showErrorToast("Error checking payment status", "userprofile-toast");
        }
      };
      checkPaymentStatus();
    }
  }, [searchParams]);

  const handleDeleteAccount = async () => {
    try {
      const csrfToken = useUserStore.getState().csrfToken;
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/delete-account/`,
        {
          method: "DELETE",
          headers: {
            Authorization: `Bearer ${sessionStorage.getItem("accessToken")}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (response.ok) {
        showSuccessToast("Account deleted successfully", "userprofile-toast");
        sessionStorage.clear();
        router.push("/login");
      } else {
        const error = await response.json();
        showErrorToast(
          error.message || "Failed to delete account",
          "userprofile-toast"
        );
      }
    } catch (error) {
      showErrorToast(
        "An error occurred while deleting account",
        "userprofile-toast"
      );
    }
    setShowDeleteConfirmation(false);
  };

  return (
    <MotionDiv
      className=" h-full flex bg-gray-100 flex-row"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.25 }}
    >
      <PaymentStatusModal
        status={paymentStatus}
        onContinue={() => {
          setPaymentStatus("idle");
          router.push("/dashboard/user-profile");
        }}
      />
      <DeleteAccountModal
        open={showDeleteConfirmation}
        onCancel={() => setShowDeleteConfirmation(false)}
        onConfirm={handleDeleteAccount}
      />
      <div className="w-full bg-gray-100 h-full flex flex-col">
        <div className="main w-full h-full overflow-y-auto md:p-0 md:m-0 pt-8 pb-8 mt-4">
          <MotionDiv
            className="grid grid-cols-1 md:grid-cols-2 gap-5 p-2"
            initial="hidden"
            animate="show"
            variants={{
              hidden: {},
              show: { transition: { staggerChildren: 0.06 } },
            }}
          >
            <ProfileInfo
              userEmail={userEmail}
              userFirstName={userFirstName}
              userLastName={userLastName}
              userRegisterDate={userRegisterDate}
              profilePhoto={profilePhoto}
              isEditingName={isEditingName}
              newFirstName={newFirstName}
              newLastName={newLastName}
              onFirstNameChange={handleFirstNameChange}
              onLastNameChange={handleLastNameChange}
              onToggleEditName={() => setIsEditingName(!isEditingName)}
              onSaveName={handleNameChange}
              onChangePassword={handleChangePassword}
              onUploadPicture={uploadPicture}
              onDeleteAccountClick={() => setShowDeleteConfirmation(true)}
            />

            <SubscriptionCard
              userPlan={userPlan}
              userPlanExpireDate={userPlanExpireDate}
              onChangePlan={() => setIsSubscriptionModalOpen(true)}
            />

            {/* Charge Token */}
            <ChargeTokenCard
              chargeValue={chargeValue}
              tokenRate={tokenRate}
              onDecrement={() =>
                setChargeValue((prev) =>
                  Math.max(0, parseInt(prev) - 100).toString()
                )
              }
              onIncrement={() =>
                setChargeValue((prev) => (parseInt(prev) + 100).toString())
              }
              onPay={() => handlePaymentToken(Number(chargeValue))}
              isPayDisabled={Number(chargeValue) === 0}
            />

            {/* Invoice History */}
            <InvoiceHistory invoices={userInvoiceHistory.results as any} />
          </MotionDiv>
        </div>
      </div>

      {isSubscriptionModalOpen && (
        <div className="fixed inset-0 bg-black/30 backdrop-blur-sm flex justify-center items-center h-dvh w-screen z-50 p-4">
          {/* Increased max-width for mobile and adjusted spacing */}
          <div className="bg-white rounded-3xl w-full max-w-[95%] md:max-w-[800px] relative my-auto h-auto">
            {/* Close button */}
            <button
              onClick={() => setIsSubscriptionModalOpen(false)}
              className="absolute right-4 top-4 p-2 hover:bg-gray-100 rounded-full transition-colors"
              aria-label="Close modal"
            >
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>

            {/* Content Container - Adjusted max-height */}
            <div className="p-6 h-full overflow-y-auto max-h-[80vh] md:max-h-[90vh] no-scrollbar">
              {/* Title */}
              <h2 className="text-2xl md:text-[32px] font-bold mb-6">
                Subscription
              </h2>

              {/* Plans Container */}
              <div className="space-y-4 md:animate-none animate-bounceUp">
                {/* Only show Free Plan if user is not on Basic or Enterprise */}
                {(!plan || plan == "Free") && (
                  <div className="border-2 border-[#E5E7EB] rounded-2xl p-5  md:flex md:flex-row justify-between items-center">
                    <div>
                      <h3 className="text-xl font-bold text-gray-900 mb-4">
                        Free
                      </h3>

                      <ul className="space-y-2 mb-4">
                        <li className="flex items-center gap-2 text-gray-600 text-sm">
                          <span>✓</span>2 Active Workspace
                        </li>
                        <li className="flex items-center gap-2 text-gray-600 text-sm">
                          <span>✓</span>2 Team Members
                        </li>
                        <li className="flex items-center gap-2 text-gray-600 text-sm">
                          <span>✓</span>
                          Free Usage Of AI
                        </li>
                        <li className="flex items-center gap-2 text-gray-600 text-sm">
                          <span>✓</span>
                          Content Library
                        </li>
                      </ul>
                    </div>

                    <div className="text-center">
                      <div className="mb-3">
                        <span className="inline-block bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm">
                          Free
                        </span>
                      </div>
                      <button className="w-full bg-gray-600 text-white px-4 py-2 rounded-full text-sm font-medium">
                        Activated
                      </button>
                      <p className="text-xs text-gray-500 mt-2">
                        Billed Monthly
                      </p>
                    </div>
                  </div>
                )}

                {/* Basic Plan */}
                <div className="border-2 border-[#E5E7EB] rounded-2xl p-5  md:flex md:flex-row justify-between items-center">
                  <div>
                    <h3 className="text-xl font-bold text-gray-900 mb-4">
                      Basic
                    </h3>

                    <ul className="space-y-2 mb-4">
                      <li className="flex items-center gap-2 text-gray-600 text-sm">
                        <span>✓</span>2 Active Workspace
                      </li>
                      <li className="flex items-center gap-2 text-gray-600 text-sm">
                        <span>✓</span>2 Team Members
                      </li>
                      <li className="flex items-center gap-2 text-gray-600 text-sm">
                        <span>✓</span>
                        Free Usage Of AI
                      </li>
                      <li className="flex items-center gap-2 text-gray-600 text-sm">
                        <span>✓</span>
                        Content Library
                      </li>
                    </ul>
                  </div>

                  <div className="text-center">
                    <div className="mb-3">
                      <div className="text-xl font-bold text-gray-900">
                        $20
                        <span className="text-xs font-normal text-gray-500">
                          /Mo
                        </span>
                      </div>
                    </div>
                    {plan == "Basic" ? (
                      <button className="w-full bg-gray-600 text-white px-4 py-2 rounded-full text-sm font-medium">
                        Activated
                      </button>
                    ) : (
                      <button
                        className="w-full bg-[#2d394b] text-white px-4 py-2 rounded-full text-sm font-medium"
                        onClick={() => handleSubscribePayment("20")}
                      >
                        Subscribe
                      </button>
                    )}
                    <p className="text-xs text-gray-500 mt-2">Billed Monthly</p>
                  </div>
                </div>

                {/* Enterprise Plan */}
                <div className="border-2 border-[#E5E7EB] rounded-2xl p-5 md:flex md:flex-row justify-between items-center">
                  <div>
                    <h3 className="text-xl font-bold text-gray-900 mb-4">
                      Enterprise
                    </h3>

                    <ul className="space-y-2 mb-4">
                      <li className="flex items-center gap-2 text-gray-600 text-sm">
                        <span>✓</span>
                        Custom Unlimited Number Of Workspace
                      </li>
                      <li className="flex items-center gap-2 text-gray-600 text-sm">
                        <span>✓</span>
                        Unlimited Team Members
                      </li>
                      <li className="flex items-center gap-2 text-gray-600 text-sm">
                        <span>✓</span>
                        Unlimited AI Usage
                      </li>
                      <li className="flex items-center gap-2 text-gray-600 text-sm">
                        <span>✓</span>
                        Unlimited Social Media Connection
                      </li>
                    </ul>
                  </div>
                  <div className="text-center">
                    {plan == "Enterprise" ? (
                      <button className="w-full bg-gray-600 text-white px-4 py-2 rounded-full text-sm font-medium">
                        Activated
                      </button>
                    ) : (
                      <a
                        href="https://forms.gle/7BJEztFF5e686opr8"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="w-full bg-[#2d394b] text-white px-4 py-2 rounded-full text-sm font-medium"
                      >
                        Contact US
                      </a>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
      {/* <ToastWrapper containerId="userprofile-toast"/> */}
    </MotionDiv>
  );
};

export default Dashboard;
