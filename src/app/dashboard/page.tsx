"use client";

import { use<PERSON><PERSON>back, useEffect, useMemo, useRef, useState } from "react";
import { SocialAccountSelectionSidebar } from "./analytics/components/shared/SocialAccountSelectionSidebar";
import { useRouter, useSearchParams } from "next/navigation";
import LoadingScreen from "~/components/loadingscreen";
import SelectWidgetModal from "~/components/selectwidgetmodal";
// Import analytics chart components
import {
  FollowersOverview<PERSON>hart,
  Reach<PERSON>hart,
  AccountsEngaged<PERSON>hart,
  GenderDemographicsChart,
  TopPostsChart,
  ViewsChart,
  TotalInteractionsChart,
  LikesChart,
  FollowerDemographicsChart,
  EngagedAudienceDemographicsChart,
  CommentsChart,
  RepliesChart,
  SharesChart,
  SavedChart,
  ProfileLinksTapsChart,
  FollowsAndUnfollowsChart,
  FollowerViews<PERSON>hart,
  AccountR<PERSON><PERSON><PERSON><PERSON><PERSON>,
  ContentType<PERSON>hart,
  FollowersCountriesChart,
} from "./analytics/components/charts";
import { mapApiToAnalytics } from "./analytics/utils/mapApiToAnalytics";
// Import MetricsOverview for dashboard cards
import { MetricsOverview } from "./analytics/components/shared/MetricsOverview";
import { getPlatformColors } from "./analytics/utils/colors";
import type { AnalyticsData } from "./analytics/types";
import { useUserStore } from "~/store/userStore";
import { useWebSocket } from "~/hooks/useWebSocket";
import { DndProvider, useDrag, useDrop } from "react-dnd";
import { motion, AnimatePresence } from "framer-motion";
import { HTML5Backend } from "react-dnd-html5-backend";
import { useSession } from "next-auth/react";
import AddSocialAccountModal from "../../components/addsocialaccountmodal";
import type { XYCoord } from "react-dnd";
// Import Radix UI Dialog components
import * as Dialog from "@radix-ui/react-dialog";

import NoSocialSelected from "./(components)/NoSocialSelected";
import { showErrorToast } from "~/components/toasts";
import { enhancedToast } from "~/components/enhanced-toast-utils";
import axios from "axios";
import React from "react";
import {
  AddMetricsModal,
  type DashboardMetricKey,
} from "~/components/AddMetricsModal";

// Define widget types and sizes
type WidgetSize = "small" | "medium" | "large" | "xlarge";

interface WidgetConfig {
  size: WidgetSize;
  minH: number;
  colSpan: number; // Number of columns this widget should span (1-5)
  rowSpan: number; // Number of rows this widget should span
  fullWidth?: boolean;
  aspectRatio?: string; // Optional aspect ratio for better sizing
}

interface Widget {
  id: string;
  type: string;
  config: WidgetConfig;
  order: number;
}

// Widget configurations with their sizes and grid spans based on analytics page patterns
const MAX_WIDGETS = 5;
const widgetConfigs = {
  // Small metric cards - 1 column
  "Total Followers": {
    size: "small",
    minH: 200,
    colSpan: 1,
    rowSpan: 1,
    height: "200px",
    chartType: "metric",
  },
  "Account Reach": {
    size: "medium",
    minH: 280,
    colSpan: 2,
    rowSpan: 1,
    height: "280px",
    chartType: "line",
  },
  "Account Engaged": {
    size: "medium",
    minH: 280,
    colSpan: 2,
    rowSpan: 1,
    height: "280px",
    chartType: "line",
  },
  // Large overview chart - 3 columns (like analytics page)
  "Overview of Followers": {
    size: "large",
    minH: 380,
    colSpan: 3,
    rowSpan: 1,
    height: "380px",
    chartType: "overview",
  },
  // Demographics charts - proper sizing for better display
  "Follower Distribution": {
    size: "medium",
    minH: 280,
    colSpan: 2,
    rowSpan: 1,
    height: "450px",
    chartType: "pie",
  },
  Gender: {
    size: "medium",
    minH: 380,
    colSpan: 2,
    rowSpan: 1,
    height: "380px",
    chartType: "demographics",
  },
  "Audience Age": {
    size: "medium",
    minH: 280,
    colSpan: 2,
    rowSpan: 1,
    height: "280px",
    chartType: "demographics",
  },
  // Reach charts - 2 columns
  "Account Reach / Cities": {
    size: "medium",
    minH: 280,
    colSpan: 2,
    rowSpan: 1,
    height: "280px",
    chartType: "bar",
  },
  "Account Reach / Content Type": {
    size: "medium",
    minH: 280,
    colSpan: 2,
    rowSpan: 1,
    height: "280px",
    chartType: "bar",
  },
  "Followers / Countries": {
    size: "medium",
    minH: 280,
    colSpan: 2,
    rowSpan: 1,
    height: "280px",
    chartType: "bar",
  },
  // Interaction charts - 2 columns
  "Account Interactions / Reel Interactions": {
    size: "medium",
    minH: 280,
    colSpan: 2,
    rowSpan: 1,
    height: "280px",
    chartType: "line",
  },
  "Account Interactions / Top Post": {
    size: "medium",
    minH: 280,
    colSpan: 2,
    rowSpan: 1,
    height: "280px",
    chartType: "grid",
  },
  // Analytics chart types - proper sizing based on analytics page
  "Likes Chart": {
    size: "medium",
    minH: 280,
    colSpan: 2,
    rowSpan: 1,
    height: "280px",
    chartType: "line",
  },
  "Comments Chart": {
    size: "medium",
    minH: 280,
    colSpan: 2,
    rowSpan: 1,
    height: "280px",
    chartType: "line",
  },
  "Shares Chart": {
    size: "medium",
    minH: 280,
    colSpan: 2,
    rowSpan: 1,
    height: "280px",
    chartType: "line",
  },
  "Saved Chart": {
    size: "medium",
    minH: 280,
    colSpan: 2,
    rowSpan: 1,
    height: "280px",
    chartType: "line",
  },
  "Profile Links Taps": {
    size: "medium",
    minH: 280,
    colSpan: 2,
    rowSpan: 1,
    height: "280px",
    chartType: "line",
  },
  "Follows and Unfollows": {
    size: "medium",
    minH: 280,
    colSpan: 2,
    rowSpan: 1,
    height: "280px",
    chartType: "line",
  },
  "Engaged Audience Demographics": {
    size: "medium",
    minH: 280,
    colSpan: 2,
    rowSpan: 1,
    height: "280px",
    chartType: "demographics",
  },
  "Replies Chart": {
    size: "medium",
    minH: 280,
    colSpan: 2,
    rowSpan: 1,
    height: "280px",
    chartType: "line",
  },
  "Views Chart": {
    size: "medium",
    minH: 280,
    colSpan: 2,
    rowSpan: 1,
    height: "280px",
    chartType: "line",
  },
} as const;

// Build a simple default widget set so charts render immediately with cached analytics
const buildDefaultWidgets = (max: number = MAX_WIDGETS): Widget[] => {
  const preferredOrder = [
    "Overview of Followers",
    "Follower Distribution",
    "Account Reach",
    "Account Reach / Content Type",
    "Followers / Countries",
    "Views Chart",
  ].filter((t) => (widgetConfigs as any)[t]);
  return preferredOrder.slice(0, max).map((type, i) => ({
    id: `default-${type}-${i}`,
    type,
    config: (widgetConfigs as any)[type],
    order: i,
  }));
};

// Allowed widgets for the Add Widget modal and dashboard
const ALLOWED_WIDGET_TYPES: readonly string[] = [
  "Overview of Followers",
  "Follower Distribution",
  "Views Chart",
  "Audience Age",
  "Gender",
  "Account Reach",
  "Account Reach / Content Type",
  "Followers / Countries",
];

// Update type definitions at the top
interface DashboardResponse {
  success: boolean;
  json: string;
}

interface DashboardConfig {
  workspace_name: {
    name: string;
    social_accounts: SocialAccount[];
  };
  platform: "instagram" | "facebook";
  lastUpdated: string;
  widgetOrder: string[];
  widgets: Widget[];
  device: "mobile" | "desktop";
  screenWidth: number;
}

type PaymentStatus = "loading" | "success" | "error" | null;

// Add interfaces for social accounts and sample data
interface SocialAccount {
  platform: "instagram" | "facebook";
  social_id: string;
  social_name: string;
  username: string;
}

interface Workspace {
  workspace_name: string;
  social_accounts: SocialAccount[];
}

// Removed unused interfaces - simplified with analytics components

// Replace DraggableWidget with new implementation
interface DraggableWidgetProps {
  widget: Widget;
  index: number;
  children: React.ReactElement;
  onRemove: (id: string) => void;
  moveWidget: (dragIndex: number, hoverIndex: number) => void;
}

// CSS Grid configuration for responsive layout
const calculateGridColumns = (screenWidth: number) => {
  if (screenWidth >= 1920) return 6; // 6 columns on 2xl screens
  if (screenWidth >= 1536) return 6; // 6 columns on xl screens (wider usage on large monitors)
  if (screenWidth >= 1280) return 6; // 6 columns on lg screens for full-width rows
  // Slightly lower the md breakpoint so 3 columns persist under mild zoom
  if (screenWidth >= 900) return 3; // 3 columns on md screens (tuned for 110% zoom)
  if (screenWidth >= 768) return 2; // 2 columns on sm screens
  return 2; // 2 columns on xs screens to allow half-width small widgets
};

const DraggableWidget = React.memo<DraggableWidgetProps>(
  ({ widget, index, children, onRemove, moveWidget }) => {
    const ref = useRef<HTMLDivElement>(null);
    const lastSwapRef = useRef<{
      index: number;
      time: number;
      dir?: "left" | "right" | "up" | "down";
      x?: number;
      y?: number;
    }>({ index: -1, time: 0 });

    const [{ isDragging }, drag] = useDrag({
      type: "WIDGET",
      item: { type: "WIDGET", id: widget.id, index },
      collect: (monitor) => ({
        isDragging: monitor.isDragging(),
      }),
    });

    const [, drop] = useDrop({
      accept: "WIDGET",
      hover(item: { type: string; id: string; index: number }, monitor) {
        if (!ref.current) return;

        const dragIndex = item.index;
        const hoverIndex = index;

        // Don't replace items with themselves
        if (dragIndex === hoverIndex) return;

        // Only handle when pointer is directly over this target
        if (!monitor.isOver({ shallow: true })) return;

        // Determine rectangle on screen
        const hoverBoundingRect = ref.current.getBoundingClientRect();

        // Dimensions and midpoints
        const hoverHeight = hoverBoundingRect.bottom - hoverBoundingRect.top;
        const hoverWidth = hoverBoundingRect.right - hoverBoundingRect.left;
        const midY = hoverHeight / 2;
        const midX = hoverWidth / 2;

        // Determine mouse position
        const clientOffset = monitor.getClientOffset() as XYCoord | null;
        if (!clientOffset) return;

        // Offsets relative to hovered rect
        const hoverClientY = clientOffset.y - hoverBoundingRect.top;
        const hoverClientX = clientOffset.x - hoverBoundingRect.left;

        // Determine primary drag axis
        const diff = monitor.getDifferenceFromInitialOffset() as XYCoord | null;
        const dx = Math.abs(diff?.x ?? 0);
        const dy = Math.abs(diff?.y ?? 0);
        const horizontal = dx >= dy * 0.8; // lean towards horizontal if close

        if (horizontal) {
          // Use relative indices to decide intended move direction and gate by X midpoint
          if (dragIndex < hoverIndex && hoverClientX < midX) return; // moving right, must pass midX
          if (dragIndex > hoverIndex && hoverClientX > midX) return; // moving left, must pass midX
        } else {
          // Gate by Y midpoint
          if (dragIndex < hoverIndex && hoverClientY < midY) return; // moving down, must pass midY
          if (dragIndex > hoverIndex && hoverClientY > midY) return; // moving up, must pass midY
        }

        // Debounce swaps to prevent rapid flipping
        const now =
          typeof performance !== "undefined" ? performance.now() : Date.now();
        if (
          lastSwapRef.current.index === hoverIndex &&
          now - lastSwapRef.current.time < 150
        ) {
          return;
        }

        // Require minimal pointer travel since last swap when hovering the same index
        if (
          lastSwapRef.current.index === hoverIndex &&
          lastSwapRef.current.x != null &&
          lastSwapRef.current.y != null
        ) {
          const dxMove = Math.abs(clientOffset.x - lastSwapRef.current.x);
          const dyMove = Math.abs(clientOffset.y - lastSwapRef.current.y);
          const minTravel = 8; // px
          if (dxMove < minTravel && dyMove < minTravel) {
            return;
          }
        }

        // Time to actually perform the action
        moveWidget(dragIndex, hoverIndex);

        // Mutate the monitor item to avoid costly index lookups
        item.index = hoverIndex;
        // Track last swap with pointer position
        lastSwapRef.current = {
          index: hoverIndex,
          time: now,
          x: clientOffset.x,
          y: clientOffset.y,
        };
      },
    });

    drag(drop(ref));

    const getWidgetClassName = (widget: Widget) => {
      const baseClasses = "transition-all duration-200 flex flex-col w-full";

      // Apply grid column span based on widget configuration
      const colSpanClasses = {
        1: "col-span-1",
        2: "col-span-2",
        3: "col-span-3",
        4: "col-span-4",
        5: "col-span-5",
      };

      const colSpanClass =
        colSpanClasses[widget.config.colSpan as keyof typeof colSpanClasses] ||
        "col-span-1";

      return `${baseClasses} ${colSpanClass}`;
    };

    const handleRemoveClick = (e: React.MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();
      onRemove(widget.id);
    };

    return (
      <div
        ref={ref}
        className={`
        transition-all duration-200 flex flex-col w-full min-w-0 
        ${isDragging ? "opacity-80" : "opacity-100"}
        group relative
        transform transition-all duration-200 ease-in-out
        touch-manipulation
        cursor-move
        select-none
      `}
        style={{
          opacity: isDragging ? 0.8 : 1,
          gridColumn: (() => {
            const sw = typeof window !== "undefined" ? window.innerWidth : 1280;
            const cols = calculateGridColumns(sw);
            const isMobile = sw < 768;

            // Default span behavior
            let responsiveSpan = isMobile
              ? cols // on mobile, take full row
              : widget.config.colSpan;
            // Force spans for specific widgets to keep them fitting rows nicely
            const isKeyWidget =
              widget.type === "Overview of Followers" ||
              widget.type === "Gender";
            if (isKeyWidget) {
              if (isMobile) {
                // Mobile: charts take full width of each row
                responsiveSpan = cols;
              } else if (sw >= 1536) {
                // xl+: Overview 4, Gender 2 (total 6)
                responsiveSpan =
                  widget.type === "Overview of Followers"
                    ? Math.min(4, cols)
                    : Math.min(2, cols);
              } else if (sw >= 1280) {
                // lg: Overview 3, Gender 3 (total 6)
                responsiveSpan = Math.min(3, cols);
              } else if (sw >= 900) {
                // md: 3 columns -> Overview 2, Gender 1 to fit first row
                responsiveSpan =
                  widget.type === "Overview of Followers"
                    ? Math.min(2, cols)
                    : Math.min(1, cols);
              } else if (sw >= 768) {
                // sm: 2 columns -> keep them half width by default
                responsiveSpan = Math.min(1, cols);
              }
            }

            return `span ${Math.min(responsiveSpan, cols)}`;
          })(),
          gridRow: `span ${widget.config.rowSpan}`,
          height: "100%",
        }}
      >
        {/* Remove Button */}
        <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-all duration-200 z-10">
          <button
            onClick={handleRemoveClick}
            className="p-2 bg-white rounded-md shadow-surface hover:bg-gray-100 transition-colors duration-200 border border-gray-100"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 text-gray-400 hover:text-gray-600 transition-colors duration-200"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>

        {/* Widget Content */}
        <div
          className={`
        flex-1 w-full
        ${getWidgetClassName(widget)}
      `}
          style={{
            minHeight:
              typeof window !== "undefined" && window.innerWidth < 768
                ? 0
                : widget.config.minH,
            height: "100%",
            aspectRatio: widget.config.aspectRatio || "auto",
          }}
        >
          {children}
        </div>
      </div>
    );
  }
);

const Dashboard = () => {
  const { getDashboard, saveDashboard, getAnalytics, socket } = useWebSocket();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [selectedPlatform, setSelectedPlatform] = useState<
    "instagram" | "facebook"
  >("instagram");
  const {
    selectedWorkspace,
    setUser,
    selectedSocial,
    selectedWorkspaceDetails,
  } = useUserStore();
  const [showSelectWidgetModal, setShowSelectWidgetModal] = useState(false);
  const [widgets, setWidgets] = useState<Widget[]>([]);
  const searchParams = useSearchParams();
  const [paymentStatus, setPaymentStatus] = useState<PaymentStatus>(null);
  const [deviceType, setDeviceType] = useState<"mobile" | "desktop">("desktop");
  const [screenWidth, setScreenWidth] = useState(0);
  const [dashboardConfig, setDashboardConfig] =
    useState<DashboardConfig | null>(null);
  const [isLoadingConfig, setIsLoadingConfig] = useState(false);
  const [showAddSocialModal, setShowAddSocialModal] = useState(false);
  const [showMetricsModal, setShowMetricsModal] = useState(false);
  const currentWorkspace = useUserStore((state) =>
    state.workspaces?.find((w) => w.workspace_name === state.selectedWorkspace)
  );
  const [containerHeight, setContainerHeight] = useState(0);
  const dashboardRef = useRef<HTMLDivElement>(null);
  const masonryRef = useRef<HTMLDivElement>(null);

  // Live analytics data state
  // Helper function to get analytics storage key
  const getAnalyticsStorageKey = (
    ws?: string | null,
    socialId?: string | null
  ) => (ws && socialId ? `analytics_${ws}_${socialId}` : null);

  // Initialize analytics data as null - will be loaded in useEffect after store is ready
  const [analyticsData, setAnalyticsData] = useState<(AnalyticsData & {[key: string]: any}) | null>(null);

  // Debug log when analytics data changes
  useEffect(() => {
    console.log("Dashboard: Analytics data updated", {
      hasData: !!analyticsData,
      reach: analyticsData?.reach?.length || 0,
      accounts_engaged: analyticsData?.accounts_engaged?.length || 0,
      follower_count: analyticsData?.follower_count?.length || 0,
    });
  }, [analyticsData]);
  const [timeRange] = useState<"7d" | "30d">("30d");
  // Loading states - start as true, will be set to false when data is loaded
  const [isAnalyticsLoading, setIsAnalyticsLoading] = useState(true);
  const isFetchingAnalyticsRef = useRef(false);
  const getAnalyticsRef = useRef(getAnalytics);
  useEffect(() => {
    getAnalyticsRef.current = getAnalytics;
  }, [getAnalytics]);

  // Load cached analytics data on component mount and when workspace/social changes
  useEffect(() => {
    const loadCachedAnalytics = () => {
      try {
        if (typeof window === "undefined") return;

        console.log("Dashboard: Loading cached analytics", {
          selectedWorkspace,
          selectedSocial: selectedSocial?.social_id,
          hasSelectedWorkspace: !!selectedWorkspace,
          hasSelectedSocial: !!selectedSocial
        });

        // Prefer scoped key based on current selection
        const scopedKey = getAnalyticsStorageKey(selectedWorkspace, selectedSocial?.social_id);
        const scoped = scopedKey ? localStorage.getItem(scopedKey) : null;
        if (scoped) {
          const parsed = JSON.parse(scoped)?.data ?? JSON.parse(scoped);
          console.log("Dashboard: Found scoped analytics cache", {
            hasReach: !!parsed?.reach,
            reachLength: parsed?.reach?.length || 0,
            keys: Object.keys(parsed || {})
          });
          setAnalyticsData(parsed);
          setIsAnalyticsLoading(false);
          return;
        }

        // Fallback: generic cache used elsewhere in the app
        const generic = localStorage.getItem("analytics_data");
        if (generic) {
          const parsed = JSON.parse(generic);
          console.log("Dashboard: Found generic analytics cache", {
            hasReach: !!parsed?.reach,
            reachLength: parsed?.reach?.length || 0,
            keys: Object.keys(parsed || {})
          });
          setAnalyticsData(parsed);
          setIsAnalyticsLoading(false);
          return;
        }

        // Ultimate fallback: if there is exactly one analytics_* entry, use it
        const keys = Object.keys(localStorage).filter((k) =>
          /^analytics_[^_]+_/.test(k)
        );
        if (keys.length === 1) {
          const lone = localStorage.getItem(keys[0]!);
          if (lone) {
            const parsed = JSON.parse(lone)?.data ?? JSON.parse(lone);
            console.log("Dashboard: Found lone analytics cache", parsed);
            setAnalyticsData(parsed);
            setIsAnalyticsLoading(false);
            return;
          }
        }

        console.log("Dashboard: No cached analytics found");
        setIsAnalyticsLoading(false);
      } catch (e) {
        console.warn("Dashboard: Error loading cached analytics", e);
        setIsAnalyticsLoading(false);
      }
    };

    loadCachedAnalytics();
  }, [selectedWorkspace, selectedSocial?.social_id]);





  // If analytics are available but no widget layout exists yet, use a sane default layout
  useEffect(() => {
    try {
      if (analyticsData && widgets.length === 0) {
        const storedDashboard = getDashboardFromLocalStorage();
        const hasStoredWidgets = !!storedDashboard?.data?.widgets?.length;
        if (!hasStoredWidgets) {
          console.log("Setting default widgets with analytics data available");
          setWidgets(buildDefaultWidgets());
        }
      }
    } catch (err) {
      console.warn("Error setting default widgets:", err);
    }
  }, [analyticsData, widgets.length]);

  // Function to get dashboard key for local storage
  const getDashboardKey = () => {
    if (!selectedWorkspace || !selectedSocial?.platform) return null;
    return `dashboard_${selectedWorkspace}_${selectedSocial.platform}`;
  };

  // Function to save dashboard to local storage
  const saveDashboardToLocalStorage = (data: DashboardConfig) => {
    const key = getDashboardKey();
    if (!key) return;

    const storageData = {
      timestamp: new Date().getTime(),
      data: data,
    };
    localStorage.setItem(key, JSON.stringify(storageData));
  };

  // Function to get dashboard from local storage
  const getDashboardFromLocalStorage = () => {
    const key = getDashboardKey();
    if (!key) return null;

    const storedData = localStorage.getItem(key);
    if (!storedData) return null;

    const parsed = JSON.parse(storedData);
    return {
      ...parsed,
      data: {
        ...parsed.data,
        platform: parsed.data.platform as "instagram" | "facebook",
      },
    };
  };

  // Function to check if stored data is stale (older than 1 hour)
  const isDataStale = (timestamp: number) => {
    const ONE_HOUR = 60 * 60 * 1000; // 1 hour in milliseconds
    return Date.now() - timestamp > ONE_HOUR;
  };

  // Load dashboard data from localStorage immediately on component mount
  useEffect(() => {
    if (!selectedSocial?.platform) return;

    const storedDashboard = getDashboardFromLocalStorage();
    if (storedDashboard) {
      const dashConfig = storedDashboard.data;

      if (dashConfig.widgets && Array.isArray(dashConfig.widgets)) {
        const orderedWidgets = [...dashConfig.widgets]
          .filter((w: Widget) => (widgetConfigs as any)[w.type])
          .map((w: any, i: number) => ({ ...w, order: typeof w.order === "number" ? w.order : i }))
          .sort((a, b) => a.order - b.order);
        setWidgets(orderedWidgets.slice(0, MAX_WIDGETS));
      }

      setDashboardConfig({
        workspace_name: {
          name: dashConfig.workspace,
          social_accounts: dashConfig.social_accounts || [],
        },
        platform: dashConfig.platform,
        lastUpdated: dashConfig.lastUpdated,
        widgetOrder: dashConfig.widgetOrder || [],
        widgets: dashConfig.widgets || [],
        device: dashConfig.device,
        screenWidth: dashConfig.screenWidth,
      });
      // Ensure we don't block UI if we have cached config
      setIsLoadingConfig(false);
    }
  }, [selectedSocial?.platform, selectedWorkspace]);

  // Update the useEffect for fetching dashboard updates
  useEffect(() => {
    const fetchDashboardUpdates = async () => {
      if (!selectedSocial?.platform || !selectedWorkspace) return;

      try {
        // Only block if we have no cached config yet
        if (!dashboardConfig || widgets.length === 0) {
          setIsLoadingConfig(true);
        }

        const config = {
          workspace_name: selectedWorkspace,
          platform: selectedSocial.platform,
          device: deviceType,
          json: JSON.stringify({
            widgets: widgets,
            widgetOrder: widgets.map((w) => w.type),
          }),
        };

        const response = await getDashboard(config);

        if (response && response.success && response.json) {
          const dashConfig = JSON.parse(response.json);

          if (dashConfig.widgets && Array.isArray(dashConfig.widgets)) {
            const orderedWidgets = [...dashConfig.widgets]
              .filter((w: Widget) => (widgetConfigs as any)[w.type])
              .map((w: any, i: number) => ({ ...w, order: typeof w.order === "number" ? w.order : i }))
              .sort((a, b) => a.order - b.order);
            setWidgets(orderedWidgets.slice(0, MAX_WIDGETS));
          }

          setDashboardConfig({
            workspace_name: {
              name: selectedWorkspace,
              social_accounts: dashConfig.social_accounts || [],
            },
            platform: selectedSocial.platform,
            lastUpdated: new Date().toISOString(),
            widgetOrder: dashConfig.widgetOrder || [],
            widgets: dashConfig.widgets || [],
            device: deviceType,
            screenWidth: window.innerWidth,
          });

          // Save to localStorage
          saveDashboardToLocalStorage(dashConfig);
        }
      } catch (err) {
        console.error("Error fetching dashboard:", err);
      } finally {
        setIsLoadingConfig(false);
      }
    };

    fetchDashboardUpdates();
  }, [selectedWorkspace, selectedSocial?.platform, deviceType, dashboardConfig, widgets.length]);

  // Update the useEffect for saving dashboard changes
  useEffect(() => {
    const saveDashboardChanges = async () => {
      if (
        !selectedWorkspace ||
        !selectedSocial?.platform ||
        isLoadingConfig ||
        widgets.length === 0
      )
        return;

      try {
        const updatedConfig = {
          workspace_name: selectedWorkspace,
          platform: selectedSocial.platform,
          device: deviceType,
          json: JSON.stringify({
            workspace_name: selectedWorkspace,
            platform: selectedSocial.platform,
            lastUpdated: new Date().toISOString(),
            widgetOrder: widgets.map((w) => w.type),
            widgets: widgets,
            device: deviceType,
            screenWidth: window.innerWidth,
            social_accounts: selectedWorkspaceDetails?.social_accounts || [],
          }),
        };

        // Save to localStorage first
        saveDashboardToLocalStorage(JSON.parse(updatedConfig.json));

        // Then save to backend
        await saveDashboard(updatedConfig);
      } catch (error) {
        console.error("Error saving dashboard:", error);
      }
    };

    // Debounce the save operation
    const timeoutId = setTimeout(() => {
      saveDashboardChanges();
    }, 1000);

    return () => clearTimeout(timeoutId);
  }, [widgets, selectedWorkspace, selectedSocial?.platform, deviceType]);

  useEffect(() => {
    const checkAuthentication = () => {
      const sessionStatus = sessionStorage.getItem("status");
      const sessionAccessToken = sessionStorage.getItem("accessToken");
      const sessionRefreshToken = sessionStorage.getItem("refreshToken");

      const localStatus = localStorage.getItem("status");
      const localAccessToken = localStorage.getItem("accessToken");
      const localRefreshToken = localStorage.getItem("refreshToken");

      const isAuthenticated =
        (sessionStatus === "authenticated" &&
          sessionAccessToken &&
          sessionRefreshToken) ||
        (localStatus === "authenticated" &&
          localAccessToken &&
          localRefreshToken);

      if (!isAuthenticated) {
        // If not authenticated, redirect to login
        window.location.href = "/login";
      } else {
        setIsLoading(false);
      }
    };

    // Start checking authentication immediately, not after a delay
    checkAuthentication();
  }, [router]);

  const renderHeaderWithIcon = (title: string) => (
    <div className="flex items-center gap-2 mb-4">
      {selectedSocial?.platform && (
        <img
          src={`/icons/performance/${selectedSocial.platform}-on.svg`}
          alt={selectedSocial.platform}
          className="w-5 h-5"
        />
      )}
      <h2 className="text-sm font-extralight  ">{title}</h2>
    </div>
  );

  // Device detection
  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      setScreenWidth(width);
      setDeviceType(width < 768 ? "mobile" : "desktop");
    };

    handleResize(); // Initial check
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Derived loading flag for dashboard main/chart area
  // Only show loading if we have no analytics data at all (not even cached)
  const isDashboardLoading =
    isLoading || (!dashboardConfig && isLoadingConfig) || (!analyticsData);

  // Skeleton components
  const SkeletonBar: React.FC<{ className?: string }> = ({
    className = "",
  }) => (
    <div className={`relative overflow-hidden bg-gray-200/80 ${className}`}>
      <div className="absolute inset-0 -translate-x-full bg-linear-to-r from-transparent via-white/50 to-transparent animate-shimmer" />
    </div>
  );

  const SkeletonCard: React.FC<{ height?: number | string }> = ({
    height = 200,
  }) => (
    <div className="bg-white rounded-lg p-5 shadow-surface relative overflow-hidden animate-soft-glow">
      {/* Title skeleton */}
      <SkeletonBar className="h-4 w-28 mb-4 rounded" />
      <div style={{ height }} className="relative">
        <SkeletonBar className="h-full w-full rounded" />
      </div>
    </div>
  );

  const OverviewSkeleton: React.FC = () => (
    <div className="w-full py-3">
      <div
        className="grid w-full gap-3 md:gap-4"
        style={{
          gridTemplateColumns:
            screenWidth < 768
              ? `repeat(3, minmax(0, 1fr))`
              : screenWidth >= 1024
              ? `repeat(6, minmax(0, 1fr))`
              : `repeat(${Math.min(6, getGridColumns())}, minmax(160px, 1fr))`,
        }}
      >
        {Array.from({ length: 6 }).map((_, i) => (
          <div
            key={i}
            className="bg-white rounded-lg p-2 md:p-3 h-16 md:h-20 dashboard-shadow"
          >
            <SkeletonBar className="h-4 w-16 mb-2 rounded" />
            <SkeletonBar className="h-3 w-12 rounded" />
          </div>
        ))}
      </div>
    </div>
  );

  const WidgetsSkeleton: React.FC = () => (
    <div
      className="grid w-full mt-4"
      style={{
        // Force columns and full width usage during loading: 1 column on mobile, 2 on larger
        gridTemplateColumns:
          screenWidth < 768
            ? `repeat(1, minmax(0, 1fr))`
            : `repeat(2, minmax(0, 1fr))`,
        gridAutoRows:
          screenWidth < 768 ? "minmax(350px, 350px)" : "minmax(220px, 380px)",
        gap: screenWidth < 768 ? "0.75rem" : "1rem",
      }}
    >
      {Array.from({ length: Math.min(MAX_WIDGETS, 5) }).map((_, i) => (
        // On mobile each skeleton should take full row; keep col-span-1 for compatibility
        <div key={i} className="w-full col-span-1">
          <SkeletonCard height={screenWidth < 768 ? 250 : 300} />
        </div>
      ))}
    </div>
  );

  // Update handlers to maintain order property
  const handleAddWidget = (widgetType: string) => {
    if (widgets.length >= MAX_WIDGETS) {
      showErrorToast("You can add up to 5 widgets");
      setShowSelectWidgetModal(false);
      return;
    }
    const configKey =
      widgetType === "Most Active Time (Daily)"
        ? "Most Active Time"
        : widgetType;
    const config = widgetConfigs[configKey as keyof typeof widgetConfigs];

    if (!config) {
      console.error("No config found for widget type:", widgetType);
      return;
    }

    if (!checkIfWidgetFits(widgetType)) {
      showErrorToast("Invalid widget type selected");
      setShowSelectWidgetModal(false);
      return;
    }

    const newWidget = {
      id: `widget-${Date.now()}`,
      type: widgetType,
      config,
      order: widgets.length,
    };

    setWidgets((prev) => {
      const updatedWidgets = [...prev, newWidget].sort(
        (a, b) => a.order - b.order
      );
      return updatedWidgets;
    });
    setShowSelectWidgetModal(false);
  };

  const handleRemoveWidget = (widgetId: string): void => {
    setWidgets((currentWidgets) => {
      const updatedWidgets = currentWidgets.filter((w) => w.id !== widgetId);
      const reorderedWidgets = updatedWidgets.map((widget, index) => ({
        ...widget,
        order: index,
      }));

      if (dashboardConfig) {
        const updatedConfig: DashboardConfig = {
          ...dashboardConfig,
          widgets: reorderedWidgets,
          widgetOrder: reorderedWidgets.map((w) => w.type),
        };
        saveDashboardToLocalStorage(updatedConfig);
        setDashboardConfig(updatedConfig);
      }

      return reorderedWidgets;
    });
  };

  const handleDragEnd = (result: any) => {
    if (!result.destination) return;

    const items = Array.from(widgets);
    const [reorderedItem] = items.splice(result.source.index, 1);
    if (reorderedItem) {
      items.splice(result.destination.index, 0, reorderedItem);

      // Update order property for all widgets
      const updatedItems = items.map((widget, index) => ({
        ...widget,
        order: index,
      }));

      setWidgets(updatedItems);
    }
  };

  useEffect(() => {
    const paymentId = searchParams?.get("payment_id");

    if (paymentId) {
      setPaymentStatus("loading");

      const checkPaymentStatus = async () => {
        try {
          const response = await axios.get(
            `${process.env.NEXT_PUBLIC_API_URL}/api/payment/webhook/?payment_id=${paymentId}`
          );
          const data = response.data;

          console.log("checking payment status: ", response);
          console.log("payment status: ", data.status.payment_status);

          if (data.status.payment_status === "paid") {
            setPaymentStatus("success");
            // Optional: Remove payment_id from URL without page reload
          } else {
            setPaymentStatus("error");
          }
        } catch (error: any) {
          const result = error.response?.data;
          console.error("Error checking payment status:", result);
          showErrorToast(
            result?.message ||
              "An unexpected error occurred. Please try again.",
            "verify-toast"
          );
          setPaymentStatus("error");
        }
      };
      checkPaymentStatus();
    }
  }, [searchParams]);

  // Removed getWidgetClassName - no longer needed with simplified DraggableWidget

  const renderSimpleMetricCard = (
    title: string,
    value: string,
    icon: string
  ) => {
    return (
      <div
        className="h-full w-full bg-white rounded-xl p-2.5 sm:p-3.5 shadow-surface transition-shadow flex flex-col justify-center min-w-0"
        style={{ minHeight: "160px" }}
      >
        <div className="flex items-center gap-2 mb-3">
          <i
            className={`fas ${icon} text-[#1565C0] text-xs sm:text-sm shrink-0`}
          ></i>
          <div className="min-w-0 flex-1">
            <h3 className="text-xs sm:text-sm font-semibold text-gray-900 truncate">
              {title}
            </h3>
          </div>
        </div>
        <div className="text-lg sm:text-xl font-bold text-[#1565C0] truncate">
          {value}
        </div>
      </div>
    );
  };

  const getPlatformColor = () => {
    switch (selectedSocial.platform) {
      case "instagram":
        return "#1877F2"; // Instagram pink
      case "facebook":
        return "#1877F2"; // Facebook blue
      default:
        return "#1877F2"; // Default blue
    }
  };

  // Removed old chart function - now using FollowersOverviewChart from analytics

  // Removed old render functions - now using analytics chart components

  // Removed renderGender function - now using GenderDemographicsChart from analytics

  // Removed chart functions that use recharts - now using analytics components

  // Removed unused chart render functions - now using analytics components or simple metric cards

  // Fetch live analytics data (normalized)
  const fetchAnalyticsData = useCallback(async () => {
    if (!selectedWorkspace || !selectedSocial) return;
    // Prevent duplicate in-flight requests (e.g., StrictMode double-invoke)
    if (isFetchingAnalyticsRef.current) return;
    // If the socket isn't open, rely on cached analytics and do not block UI
    if (!socket || socket.readyState !== WebSocket.OPEN) {
      setIsAnalyticsLoading(false);
      return;
    }
    try {
      isFetchingAnalyticsRef.current = true;
      // Never block UI when fetching fresh data - we always have cached data to show
      // Only set loading to true if we have no data at all
      if (!analyticsData) {
        setIsAnalyticsLoading(true);
      }

      const endTime = new Date();
      const startTime = new Date();
      const daysToSubtract = timeRange === "7d" ? 7 : 30;
      startTime.setDate(endTime.getDate() - daysToSubtract);
      const fmt = (d: Date) => d.toISOString().split("T")[0];

      const response = await getAnalyticsRef.current({
        workspace_name: selectedWorkspace,
        social_id: selectedSocial.social_id,
        start_time: fmt(startTime),
        end_time: fmt(endTime),
      });

      // Preserve top_posts like analytics page does
      const topPosts =
        (response as any)?.data?.top_posts ??
        (response as any)?.data?.analytics?.top_posts ??
        (response as any)?.analytics_data?.top_posts ??
        (response as any)?.analytics?.top_posts ??
        (response as any)?.top_posts;

      const mapped = mapApiToAnalytics(response);
      if (mapped) {
        const merged: any = Array.isArray(topPosts)
          ? { ...mapped, top_posts: topPosts }
          : mapped;
        setAnalyticsData(merged as any);
        // Persist to generic key for analytics page compatibility
        localStorage.setItem("analytics_data", JSON.stringify(merged));
        // Also persist to workspace-scoped key (aligns with DashboardLayout use)
        const scopedKey = getAnalyticsStorageKey(
          selectedWorkspace,
          selectedSocial?.social_id ?? null
        );
        if (scopedKey) {
          localStorage.setItem(
            scopedKey,
            JSON.stringify({ timestamp: Date.now(), data: merged })
          );
        }
      } else {
        // Keep existing cached analytics if mapping yields no series
        console.warn("Analytics mapping returned empty; retaining cached data");
      }
    } catch (e) {
      console.error("Dashboard analytics fetch error:", e);
      // Do not clear cached analytics on fetch errors; keep showing stored data
    } finally {
      isFetchingAnalyticsRef.current = false;
      setIsAnalyticsLoading(false);
    }
  }, [selectedWorkspace, selectedSocial, timeRange, socket, analyticsData]);

  useEffect(() => {
    if (selectedWorkspace && selectedSocial) {
      fetchAnalyticsData();
    }
    // Intentionally not depending on fetchAnalyticsData identity to avoid duplicate calls
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedWorkspace, selectedSocial, timeRange, socket?.readyState]);

  // Persist a compact summary, mirroring analytics page behavior
  const computeAnalyticsSummary = useCallback((data: any) => {
    if (!data) return null;
    const sumValues = (obj: Record<string, number>) =>
      Object.values(obj).reduce((s, v) => s + (Number(v) || 0), 0);
    const aggregateBy = (arr: any[] | undefined, key: string) => {
      const map: Record<string, number> = {};
      if (!Array.isArray(arr)) return map;
      arr.forEach((dp: any) => {
        const k = dp?.breakdown?.[key] ?? dp?.[key];
        if (!k) return;
        const val = Number(dp?.total_value ?? dp?.value ?? 0) || 0;
        map[String(k)] = (map[String(k)] || 0) + val;
      });
      return map;
    };
    const demo = Array.isArray(data?.follower_demographics)
      ? data.follower_demographics
      : [];
    const ageMap = aggregateBy(demo, "age");
    const genderMap = aggregateBy(demo, "gender");
    const reach = Array.isArray(data?.reach) ? data.reach : [];
    const countrySrc = aggregateBy(demo, "country");
    const citySrcFromReach = aggregateBy(reach, "city");
    const countryMap = Object.keys(countrySrc).length
      ? countrySrc
      : aggregateBy(reach, "country");
    const cityMap = Object.keys(citySrcFromReach).length
      ? citySrcFromReach
      : aggregateBy(demo, "city");
    const toPercent = (map: Record<string, number>) => {
      const total = sumValues(map) || 0;
      if (!total) return {} as Record<string, number>;
      const out: Record<string, number> = {};
      Object.entries(map).forEach(([k, v]) => {
        out[k] = Math.round(((Number(v) || 0) / total) * 100);
      });
      const sumPct = Object.values(out).reduce((s, v) => s + v, 0);
      const entries = Object.entries(out) as [string, number][];
      if (sumPct !== 100 && entries.length) {
        const [largestKey] = entries.reduce((max, cur) =>
          cur[1] > max[1] ? cur : max
        );
        out[largestKey] = (out[largestKey] || 0) + (100 - sumPct);
      }
      return out;
    };
    const toTop = (map: Record<string, number>, limit = 25) =>
      Object.entries(map)
        .sort((a, b) => (Number(b[1]) || 0) - (Number(a[1]) || 0))
        .slice(0, limit)
        .map(([name, value]) => ({ name, value }));
    return {
      age: toPercent(ageMap),
      gender: toPercent(genderMap),
      country: toPercent(countryMap),
      city: toPercent(cityMap),
      topCountries: toTop(countryMap, 50),
      topCities: toTop(cityMap, 50),
    };
  }, []);

  useEffect(() => {
    if (!analyticsData) return;
    try {
      const summary = computeAnalyticsSummary(analyticsData);
      if (summary) {
        localStorage.setItem("analytics_summary", JSON.stringify(summary));
      }
    } catch (e) {
      console.warn("Failed to persist analytics_summary:", e);
    }
  }, [analyticsData, computeAnalyticsSummary]);

  // Removed unused render functions - now using analytics chart components

  // Derived datasets from live analytics
  const contentTypeData = useMemo(() => {
    const map: Record<string, number> = {};
    const reachArr = Array.isArray(analyticsData?.reach)
      ? (analyticsData as any).reach
      : [];
    reachArr.forEach((dp: any) => {
      const t =
        dp?.breakdown?.media_product_type ||
        dp?.breakdown?.media_type ||
        dp?.media_product_type;
      const val = Number(dp?.total_value ?? 0) || 0;
      if (typeof t === "string") map[t] = (map[t] || 0) + val;
    });
    const label = (k: string) => {
      const s = k.toLowerCase();
      if (s.includes("reel")) return "Reels";
      if (s.includes("story")) return "Stories";
      if (s.includes("video")) return "Videos";
      if (s.includes("post") || s.includes("carousel")) return "Posts";
      return k;
    };
    return Object.entries(map).map(([k, v]) => ({ name: label(k), value: v }));
  }, [analyticsData]);

  const cityData = useMemo(() => {
    const map: Record<string, number> = {};
    const reachArr = Array.isArray(analyticsData?.reach)
      ? (analyticsData as any).reach
      : [];
    reachArr.forEach((dp: any) => {
      const city = dp?.breakdown?.city || dp?.city;
      const val = Number(dp?.total_value ?? 0) || 0;
      if (city) map[String(city)] = (map[String(city)] || 0) + val;
    });
    if (Object.keys(map).length === 0) {
      const demoArr = Array.isArray(analyticsData?.follower_demographics)
        ? (analyticsData as any).follower_demographics
        : [];
      demoArr.forEach((dp: any) => {
        const city = dp?.breakdown?.city || dp?.city;
        const val = Number(dp?.total_value ?? 0) || 0;
        if (city) map[String(city)] = (map[String(city)] || 0) + val;
      });
    }
    if (Object.keys(map).length === 0 && analyticsData) {
      Object.values(analyticsData as any).forEach((arr: any) => {
        if (!Array.isArray(arr)) return;
        arr.forEach((dp: any) => {
          const city = dp?.breakdown?.city || dp?.city;
          const val = Number(dp?.total_value ?? 0) || 0;
          if (city) map[String(city)] = (map[String(city)] || 0) + val;
        });
      });
    }
    return Object.entries(map).map(([name, value]) => ({ name, value }));
  }, [analyticsData]);

  const followersOverviewData = useMemo(() => {
    const gainsSeries: any[] = Array.isArray(analyticsData?.follower_count)
      ? [...(analyticsData as any).follower_count]
      : [];
    const normalized = gainsSeries
      .map((dp: any) => {
        const dateStr: string | undefined = dp?.date
          ? String(dp.date).split("T")[0]
          : dp?.end_time
          ? String(dp.end_time).split("T")[0]
          : undefined;
        const value = Number(dp?.total_value ?? dp?.value ?? 0) || 0;
        const ts = dateStr ? new Date(dateStr).getTime() : 0;
        return { date: dateStr, total_value: value, _ts: ts };
      })
      .filter((d: any) => !!d.date)
      .sort((a: any, b: any) => a._ts - b._ts);
    if (!normalized.length) return gainsSeries;
    const latestTotal = (analyticsData as any)?.followers_count;
    if (typeof latestTotal !== "number" || !isFinite(latestTotal)) {
      return normalized;
    }
    const sumGains = normalized.reduce(
      (s: number, d: any) => s + (Number(d.total_value) || 0),
      0
    );
    let running = Number(latestTotal) - sumGains;
    return normalized.map((d: any) => {
      running += Number(d.total_value) || 0;
      return { date: d.date, total_value: running < 0 ? 0 : running };
    });
  }, [analyticsData]);

  // ===== Dashboard Metrics Cards: similar to analytics but with add/remove functionality =====
  interface MetricsCardData {
    label: string;
    value: string | number;
    icon: string;
    color: string;
    bgColor: string;
    change?: {
      value: number;
      valueStr: string;
      isPositive: boolean;
      rawChange: number;
    };
  }

  // Available metrics based on analytics data structure
  const AVAILABLE_METRICS = [
    "followers",
    "following",
    "media_count",
    "new_followers",
    "unfollowers",
    "profile_links_taps",
    "replies",
    "saves",
    "accounts_engaged",
    "reach",
    "likes",
    "comments",
    "total_interactions",
    "views",
  ] as const;

  const [selectedMetricsCards, setSelectedMetricsCards] = useState<
    DashboardMetricKey[]
  >(["followers", "reach", "likes", "comments", "total_interactions", "views"]);

  const availableMetricsCards = useMemo(() => {
    return AVAILABLE_METRICS.filter((k) => !selectedMetricsCards.includes(k));
  }, [selectedMetricsCards]);

  // Helper function to calculate change between day 1 and the last day (copied from analytics)
  const calculateChange = (dataArray: any[], _timeRange: "7d" | "30d") => {
    if (!dataArray || dataArray.length === 0) return null;

    const dated = dataArray
      .filter((item) => item?.date)
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

    if (dated.length < 2) return null;

    const first = Number(dated[0]?.total_value ?? 0) || 0;
    const last = Number(dated[dated.length - 1]?.total_value ?? 0) || 0;

    if (first <= 0) return null;

    const percentageChange = ((last - first) / first) * 100;
    const absPct = Math.abs(percentageChange);
    const displayPct =
      absPct > 0 && absPct < 0.1 ? 0.1 : Number(absPct.toFixed(1));

    return {
      value: displayPct,
      valueStr: `${displayPct}%`,
      isPositive: percentageChange >= 0,
      rawChange: last - first,
    };
  };

  // Helper to get total value from API response (copied from analytics)
  const getTotalValue = (metric: any): number | undefined => {
    const direct = metric?.total_value?.value;
    if (typeof direct === "number" && Number.isFinite(direct)) return direct;

    if (Array.isArray(metric)) {
      const aggregate = metric.find(
        (it: any) =>
          (it?.date == null || it?.date === undefined) && it?.breakdown == null
      );
      const val = aggregate?.total_value;
      if (typeof val === "number" && Number.isFinite(val)) return val;
    }

    return undefined;
  };

  // Generate metrics cards similar to analytics but adapted for dashboard
  const dashboardMetricsCards = useMemo((): MetricsCardData[] => {
    if (!analyticsData) return [];

    const metrics: MetricsCardData[] = [];
    const platformColors = getPlatformColors(selectedSocial?.platform);

    // Helper for follows/unfollows calculation
    const computeFollowsUnfollows = () => {
      const series = Array.isArray(analyticsData.follows_and_unfollows)
        ? analyticsData.follows_and_unfollows
        : [];
      if (series.length === 0) return { newFollowers: 0, unfollowers: 0 };

      const sumBy = (predicate: (it: any) => boolean) =>
        series.reduce(
          (acc: number, it: any) =>
            acc + (predicate(it) ? Number(it?.total_value) || 0 : 0),
          0
        );

      const isType = (it: any, types: string[]) => {
        const t = String(it?.breakdown?.follow_type || "").toUpperCase();
        return types.includes(t);
      };

      return {
        newFollowers: sumBy((it) =>
          isType(it, ["FOLLOW", "FOLLOWER", "FOLLOWED"])
        ),
        unfollowers: sumBy((it) =>
          isType(it, ["UNFOLLOW", "NON_FOLLOWER", "UNFOLLOWED"])
        ),
      };
    };

    const followsData = computeFollowsUnfollows();

    // Map each metric
    selectedMetricsCards.forEach((key) => {
      switch (key) {
        case "followers":
          const followersSnapshot =
            typeof analyticsData.followers_count === "number"
              ? analyticsData.followers_count
              : Array.isArray(analyticsData.follower_count) &&
                analyticsData.follower_count.length > 0
              ? Number(
                  analyticsData.follower_count[
                    analyticsData.follower_count.length - 1
                  ]?.total_value ?? 0
                )
              : undefined;
          if (
            typeof followersSnapshot === "number" &&
            !Number.isNaN(followersSnapshot)
          ) {
            metrics.push({
              label: "Followers",
              value: followersSnapshot.toLocaleString(),
              icon: "fa-user-friends",
              color: platformColors.iconColor,
              bgColor: platformColors.metricIconBgColor,
            });
          }
          break;

        case "following":
          const followsSnapshot =
            typeof analyticsData.follows_count === "number"
              ? analyticsData.follows_count
              : undefined;
          if (
            typeof followsSnapshot === "number" &&
            !Number.isNaN(followsSnapshot)
          ) {
            metrics.push({
              label: "Following",
              value: followsSnapshot.toLocaleString(),
              icon: "fa-user-plus",
              color: platformColors.iconColor,
              bgColor: platformColors.metricIconBgColor,
            });
          }
          break;

        case "media_count":
          const mediaSnapshot =
            typeof analyticsData.media_count === "number"
              ? analyticsData.media_count
              : undefined;
          if (
            typeof mediaSnapshot === "number" &&
            !Number.isNaN(mediaSnapshot)
          ) {
            metrics.push({
              label: "Media Count",
              value: mediaSnapshot.toLocaleString(),
              icon: "fa-photo-video",
              color: platformColors.iconColor,
              bgColor: platformColors.metricIconBgColor,
            });
          }
          break;

        case "new_followers":
          if (followsData.newFollowers >= 0) {
            metrics.push({
              label: "New Followers",
              value: followsData.newFollowers.toLocaleString(),
              icon: "fa-user-plus",
              color: platformColors.iconColor,
              bgColor: platformColors.metricIconBgColor,
            });
          }
          break;

        case "unfollowers":
          if (followsData.unfollowers >= 0) {
            metrics.push({
              label: "Unfollowers",
              value: followsData.unfollowers.toLocaleString(),
              icon: "fa-user-minus",
              color: platformColors.iconColor,
              bgColor: platformColors.metricIconBgColor,
            });
          }
          break;

        case "profile_links_taps":
          const profileLinksTotal = getTotalValue(
            analyticsData.profile_links_taps
          );
          const profileLinksChange = calculateChange(
            Array.isArray(analyticsData.profile_links_taps)
              ? analyticsData.profile_links_taps
              : [],
            timeRange
          );
          if (
            typeof profileLinksTotal === "number" ||
            profileLinksTotal === 0
          ) {
            metrics.push({
              label: "Profile Links Taps",
              value: (profileLinksTotal || 0).toLocaleString(),
              icon: "fa-link",
              color: platformColors.iconColor,
              bgColor: platformColors.metricIconBgColor,
              change: profileLinksChange || undefined,
            });
          }
          break;

        case "replies":
          const repliesTotal = getTotalValue(analyticsData.replies);
          const repliesChange = calculateChange(
            Array.isArray(analyticsData.replies) ? analyticsData.replies : [],
            timeRange
          );
          if (typeof repliesTotal === "number" || repliesTotal === 0) {
            metrics.push({
              label: "Replies",
              value: (repliesTotal || 0).toLocaleString(),
              icon: "fa-reply",
              color: platformColors.iconColor,
              bgColor: platformColors.metricIconBgColor,
              change: repliesChange || undefined,
            });
          }
          break;

        case "saves":
          const savesTotal = getTotalValue(analyticsData.saves);
          const savesChange = calculateChange(
            Array.isArray(analyticsData.saves) ? analyticsData.saves : [],
            timeRange
          );
          if (typeof savesTotal === "number" || savesTotal === 0) {
            metrics.push({
              label: "Saves",
              value: (savesTotal || 0).toLocaleString(),
              icon: "fa-bookmark",
              color: platformColors.iconColor,
              bgColor: platformColors.metricIconBgColor,
              change: savesChange || undefined,
            });
          }
          break;

        case "accounts_engaged":
          const engagedTotal = getTotalValue(analyticsData.accounts_engaged);
          const engagedChange = calculateChange(
            Array.isArray(analyticsData.accounts_engaged)
              ? analyticsData.accounts_engaged
              : [],
            timeRange
          );
          // Show accounts engaged even if 0
          metrics.push({
            label: "Accounts Engaged",
            value: (engagedTotal || 0).toLocaleString(),
            icon: "fa-users",
            color: platformColors.iconColor,
            bgColor: platformColors.metricIconBgColor,
            change: engagedChange || undefined,
          });
          break;

        case "reach":
          const reachTotal = getTotalValue(analyticsData.reach);
          const reachChange = calculateChange(
            Array.isArray(analyticsData.reach) ? analyticsData.reach : [],
            timeRange
          );
          // Show reach even if 0
          metrics.push({
            label: "Total Reach",
            value: (reachTotal || 0).toLocaleString(),
            icon: "fa-eye",
            color: platformColors.iconColor,
            bgColor: platformColors.metricIconBgColor,
            change: reachChange || undefined,
          });
          break;

        case "likes":
          const likesTotal = getTotalValue(analyticsData.likes);
          const likesChange = calculateChange(
            Array.isArray(analyticsData.likes) ? analyticsData.likes : [],
            timeRange
          );
          // Show likes even if 0
          metrics.push({
            label: "Total Likes",
            value: (likesTotal || 0).toLocaleString(),
            icon: "fa-heart",
            color: platformColors.iconColor,
            bgColor: platformColors.metricIconBgColor,
            change: likesChange || undefined,
          });
          break;

        case "comments":
          const commentsTotal = getTotalValue(analyticsData.comments);
          const commentsChange = calculateChange(
            Array.isArray(analyticsData.comments) ? analyticsData.comments : [],
            timeRange
          );
          // Show comments even if 0
          metrics.push({
            label: "Total Comments",
            value: (commentsTotal || 0).toLocaleString(),
            icon: "fa-comment",
            color: platformColors.iconColor,
            bgColor: platformColors.metricIconBgColor,
            change: commentsChange || undefined,
          });
          break;

        case "total_interactions":
          const interactionsTotal = getTotalValue(
            analyticsData.total_interactions
          );
          const interactionsChange = calculateChange(
            Array.isArray(analyticsData.total_interactions)
              ? analyticsData.total_interactions
              : [],
            timeRange
          );
          // Show total interactions even if 0
          metrics.push({
            label: "Total Interactions",
            value: (interactionsTotal || 0).toLocaleString(),
            icon: "fa-chart-line",
            color: platformColors.iconColor,
            bgColor: platformColors.metricIconBgColor,
            change: interactionsChange || undefined,
          });
          break;

        case "views":
          const viewsTotal = getTotalValue(analyticsData.views);
          const viewsChange = calculateChange(
            Array.isArray(analyticsData.views) ? analyticsData.views : [],
            timeRange
          );
          // Show views even if 0
          metrics.push({
            label: "Total Views",
            value: (viewsTotal || 0).toLocaleString(),
            icon: "fa-play",
            color: platformColors.iconColor,
            bgColor: platformColors.metricIconBgColor,
            change: viewsChange || undefined,
          });
          break;
      }
    });

    return metrics;
  }, [
    analyticsData,
    selectedMetricsCards,
    selectedSocial?.platform,
    timeRange,
  ]);

  // Build preview data map for ALL metrics (used by AddMetricsModal previews)
  const metricPreviewData = useMemo((): Partial<
    Record<
      DashboardMetricKey,
      {
        label: string;
        value: string | number;
        icon?: string;
        change?: { valueStr?: string; value?: number; isPositive?: boolean };
      }
    >
  > => {
    if (!analyticsData) return {};

    const map: Partial<Record<DashboardMetricKey, any>> = {};
    const platformColors = getPlatformColors(selectedSocial?.platform);

    const computeFollowsUnfollows = () => {
      const series = Array.isArray(analyticsData.follows_and_unfollows)
        ? analyticsData.follows_and_unfollows
        : [];
      if (series.length === 0) return { newFollowers: 0, unfollowers: 0 };

      const sumBy = (predicate: (it: any) => boolean) =>
        series.reduce(
          (acc: number, it: any) =>
            acc + (predicate(it) ? Number(it?.total_value) || 0 : 0),
          0
        );

      const isType = (it: any, types: string[]) => {
        const t = String(it?.breakdown?.follow_type || "").toUpperCase();
        return types.includes(t);
      };

      return {
        newFollowers: sumBy((it) =>
          isType(it, ["FOLLOW", "FOLLOWER", "FOLLOWED"])
        ),
        unfollowers: sumBy((it) =>
          isType(it, ["UNFOLLOW", "NON_FOLLOWER", "UNFOLLOWED"])
        ),
      };
    };

    const followsData = computeFollowsUnfollows();

    const push = (
      key: DashboardMetricKey,
      payload: { label: string; value?: number; icon: string; change?: any }
    ) => {
      const valStr = (payload.value ?? 0).toLocaleString();
      map[key] = {
        label: payload.label,
        value: valStr,
        icon: payload.icon,
        change: payload.change,
        color: platformColors.iconColor,
        bgColor: platformColors.metricIconBgColor,
      } as any;
    };

    const reachTotal = getTotalValue(analyticsData.reach);
    const reachChange = calculateChange(
      Array.isArray(analyticsData.reach) ? analyticsData.reach : [],
      timeRange
    );

    const likesTotal = getTotalValue(analyticsData.likes);
    const likesChange = calculateChange(
      Array.isArray(analyticsData.likes) ? analyticsData.likes : [],
      timeRange
    );

    const commentsTotal = getTotalValue(analyticsData.comments);
    const commentsChange = calculateChange(
      Array.isArray(analyticsData.comments) ? analyticsData.comments : [],
      timeRange
    );

    const interactionsTotal = getTotalValue(analyticsData.total_interactions);
    const interactionsChange = calculateChange(
      Array.isArray(analyticsData.total_interactions)
        ? analyticsData.total_interactions
        : [],
      timeRange
    );

    const viewsTotal = getTotalValue(analyticsData.views);
    const viewsChange = calculateChange(
      Array.isArray(analyticsData.views) ? analyticsData.views : [],
      timeRange
    );

    const profileLinksTotal = getTotalValue(analyticsData.profile_links_taps);
    const profileLinksChange = calculateChange(
      Array.isArray(analyticsData.profile_links_taps)
        ? analyticsData.profile_links_taps
        : [],
      timeRange
    );

    const repliesTotal = getTotalValue(analyticsData.replies);
    const repliesChange = calculateChange(
      Array.isArray(analyticsData.replies) ? analyticsData.replies : [],
      timeRange
    );

    const savesTotal = getTotalValue(analyticsData.saves);
    const savesChange = calculateChange(
      Array.isArray(analyticsData.saves) ? analyticsData.saves : [],
      timeRange
    );

    const engagedTotal = getTotalValue(analyticsData.accounts_engaged);
    const engagedChange = calculateChange(
      Array.isArray(analyticsData.accounts_engaged)
        ? analyticsData.accounts_engaged
        : [],
      timeRange
    );

    const followersSnapshot =
      typeof analyticsData.followers_count === "number"
        ? analyticsData.followers_count
        : Array.isArray(analyticsData.follower_count) &&
          analyticsData.follower_count.length > 0
        ? Number(
            analyticsData.follower_count[
              analyticsData.follower_count.length - 1
            ]?.total_value ?? 0
          )
        : undefined;

    const followsSnapshot =
      typeof analyticsData.follows_count === "number"
        ? analyticsData.follows_count
        : undefined;

    const mediaSnapshot =
      typeof analyticsData.media_count === "number"
        ? analyticsData.media_count
        : undefined;

    // Fill map for each available metric key
    AVAILABLE_METRICS.forEach((key) => {
      switch (key) {
        case "followers":
          if (typeof followersSnapshot === "number")
            push("followers", {
              label: "Followers",
              value: followersSnapshot,
              icon: "fa-user-friends",
            });
          break;
        case "following":
          if (typeof followsSnapshot === "number")
            push("following", {
              label: "Following",
              value: followsSnapshot,
              icon: "fa-user-plus",
            });
          break;
        case "media_count":
          if (typeof mediaSnapshot === "number")
            push("media_count", {
              label: "Media Count",
              value: mediaSnapshot,
              icon: "fa-photo-video",
            });
          break;
        case "new_followers":
          push("new_followers", {
            label: "New Followers",
            value: followsData.newFollowers,
            icon: "fa-user-plus",
          });
          break;
        case "unfollowers":
          push("unfollowers", {
            label: "Unfollowers",
            value: followsData.unfollowers,
            icon: "fa-user-minus",
          });
          break;
        case "profile_links_taps":
          push("profile_links_taps", {
            label: "Profile Links Taps",
            value: (profileLinksTotal as number) ?? 0,
            icon: "fa-link",
            change: profileLinksChange || undefined,
          });
          break;
        case "replies":
          push("replies", {
            label: "Replies",
            value: (repliesTotal as number) ?? 0,
            icon: "fa-reply",
            change: repliesChange || undefined,
          });
          break;
        case "saves":
          push("saves", {
            label: "Saves",
            value: (savesTotal as number) ?? 0,
            icon: "fa-bookmark",
            change: savesChange || undefined,
          });
          break;
        case "accounts_engaged":
          push("accounts_engaged", {
            label: "Accounts Engaged",
            value: (engagedTotal as number) ?? 0,
            icon: "fa-users",
            change: engagedChange || undefined,
          });
          break;
        case "reach":
          push("reach", {
            label: "Total Reach",
            value: (reachTotal as number) ?? 0,
            icon: "fa-eye",
            change: reachChange || undefined,
          });
          break;
        case "likes":
          push("likes", {
            label: "Total Likes",
            value: (likesTotal as number) ?? 0,
            icon: "fa-heart",
            change: likesChange || undefined,
          });
          break;
        case "comments":
          push("comments", {
            label: "Total Comments",
            value: (commentsTotal as number) ?? 0,
            icon: "fa-comment",
            change: commentsChange || undefined,
          });
          break;
        case "total_interactions":
          push("total_interactions", {
            label: "Total Interactions",
            value: (interactionsTotal as number) ?? 0,
            icon: "fa-chart-line",
            change: interactionsChange || undefined,
          });
          break;
        case "views":
          push("views", {
            label: "Total Views",
            value: (viewsTotal as number) ?? 0,
            icon: "fa-play",
            change: viewsChange || undefined,
          });
          break;
      }
    });

    return map;
  }, [analyticsData, selectedSocial?.platform, timeRange]);

  const addMetricCard = (key: DashboardMetricKey) => {
    setSelectedMetricsCards((prev) => {
      if (prev.includes(key)) return prev;
      const next = [...prev];
      // Cap to 6 cards in first row to maintain one-row limit
      if (next.length >= 6) return next;
      next.push(key);
      return next;
    });
    setShowMetricsModal(false);
  };

  const removeMetricCard = (key: DashboardMetricKey) => {
    setSelectedMetricsCards((prev) => prev.filter((k) => k !== key));
  };

  const moveMetricCard = (fromIndex: number, toIndex: number) => {
    setSelectedMetricsCards((prev) => {
      if (
        fromIndex < 0 ||
        toIndex < 0 ||
        fromIndex >= prev.length ||
        toIndex >= prev.length
      ) {
        return prev;
      }
      const next = [...prev];
      const removed = next.splice(fromIndex, 1)[0];
      if (removed === undefined) return prev;
      next.splice(toIndex, 0, removed);
      return next;
    });
  };

  const METRICS_DND_TYPE = "DASHBOARD_METRIC" as const;

  // Metrics Card component with drag/drop functionality
  const DashboardMetricCard: React.FC<{
    index: number;
    metric: MetricsCardData;
    metricKey: DashboardMetricKey;
  }> = ({ index, metric, metricKey }) => {
    const ref = useRef<HTMLDivElement | null>(null);

    const [, drop] = useDrop<{ index: number } | null>({
      accept: METRICS_DND_TYPE,
      hover: (item) => {
        if (!ref.current || !item) return;
        const dragIndex = item.index;
        const hoverIndex = index;
        if (dragIndex === hoverIndex) return;
        moveMetricCard(dragIndex, hoverIndex);
        item.index = hoverIndex;
      },
    });

    const [{ isDragging }, drag] = useDrag({
      type: METRICS_DND_TYPE,
      item: { index },
      collect: (monitor) => ({ isDragging: monitor.isDragging() }),
    });

    drag(drop(ref));

    return (
      <motion.div
        ref={ref}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -10 }}
        transition={{ duration: 0.2, ease: "easeOut" }}
        className="bg-white rounded-lg p-2 py-3 md:p-4 text-center dashboard-shadow transition-shadow min-w-0 shrink-0 relative"
        style={{ opacity: isDragging ? 0.6 : 1 }}
      >
        <button
          className="absolute top-2 right-2 text-gray-400 hover:text-gray-600 text-xs"
          aria-label={`Remove ${metric.label}`}
          onClick={() => removeMetricCard(metricKey)}
        >
          <i className="fas fa-times" />
        </button>

        <div className="relative">
          <div className="text-base md:text-xl font-bold text-gray-900 mb-1 truncate">
            {typeof metric.value === "number"
              ? metric.value.toLocaleString()
              : metric.value}
          </div>
          {/* Change indicator positioned at top right */}
          {metric.change && (
            <div
              className={`absolute top-0 -right-1 md:-right-2 text-xs md:text-sm font-semibold ${
                metric.change.isPositive ? "text-green-600" : "text-red-600"
              }`}
            >
              {metric.change.isPositive ? "+" : "-"}
              {metric.change.valueStr ?? `${metric.change.value}%`}
            </div>
          )}
        </div>
        <div className="text-xs md:text-sm text-gray-600 truncate">
          {metric.label}
        </div>
      </motion.div>
    );
  };

  const renderWidget = (widget: Widget) => {
    const config = widget.config as any; // Cast to access height property
    const chartHeight = screenWidth < 768 ? "350px" : "380px"; // +30px on mobile

    const renderWidgetContent = () => {
      // Debug log to see what analytics data is available
      if (widget.type === "Account Reach") {
        console.log("Rendering Account Reach widget:", {
          hasAnalyticsData: !!analyticsData,
          reachDataLength: analyticsData?.reach?.length || 0,
          reachData: analyticsData?.reach,
          fullAnalyticsKeys: analyticsData ? Object.keys(analyticsData) : []
        });
      }

      switch (widget.type) {
        case "Total Followers":
          return renderSimpleMetricCard(
            "Total Followers",
            "12,543",
            "fa-users"
          );
        case "Account Reach":
          return (
            <ReachChart
              data={analyticsData?.reach || []}
              timeRange="30d"
              selectedSocial={selectedSocial}
              height={chartHeight}
            />
          );
        case "Account Engaged":
          return (
            <AccountsEngagedChart
              data={analyticsData?.accounts_engaged || []}
              timeRange="30d"
              selectedSocial={selectedSocial}
              height={chartHeight}
            />
          );
        case "Overview of Followers":
          return (
            <FollowersOverviewChart
              data={followersOverviewData || []}
              timeRange="30d"
              selectedSocial={selectedSocial}
              height={chartHeight}
            />
          );
        case "Gender":
          return (
            <GenderDemographicsChart
              data={analyticsData?.follower_demographics || []}
              title="Gender Distribution"
              description="Gender breakdown of your audience"
              selectedSocial={selectedSocial}
              height={chartHeight}
            />
          );
        case "Audience Age":
          return (
            <FollowerDemographicsChart
              data={analyticsData?.follower_demographics || []}
              selectedSocial={selectedSocial}
              height={chartHeight}
            />
          );
        case "Account Reach / Cities":
          return (
            <AccountReachCitiesChart
              data={cityData}
              timeRange="30d"
              height={chartHeight}
            />
          );
        case "Followers / Countries":
          return (
            <FollowersCountriesChart
              data={analyticsData?.follower_demographics || []}
              selectedSocial={selectedSocial}
              height={chartHeight}
            />
          );
        case "Account Reach / Content Type":
          return (
            <ContentTypeChart
              data={contentTypeData}
              timeRange="30d"
              selectedSocial={selectedSocial}
              height={chartHeight}
            />
          );
        case "Follower Distribution":
          return (
            <FollowerViewsChart
              data={analyticsData?.views || []}
              timeRange="30d"
              selectedSocial={selectedSocial}
              height={chartHeight}
            />
          );
        case "Account Interactions / Top Post":
          return (
            <TopPostsChart
              data={(analyticsData as any)?.top_posts || []}
              type="posts"
              selectedSocial={selectedSocial}
              height={chartHeight}
            />
          );
        case "Account Interactions / Reel Interactions":
          return (
            <TotalInteractionsChart
              data={analyticsData?.total_interactions || []}
              timeRange="30d"
              selectedSocial={selectedSocial}
              height={chartHeight}
            />
          );
        // Analytics chart types with proper props
        case "Likes Chart":
          return (
            <LikesChart
              data={analyticsData?.likes || []}
              timeRange="30d"
              selectedSocial={selectedSocial}
              height={chartHeight}
            />
          );
        case "Comments Chart":
          return (
            <CommentsChart
              data={analyticsData?.comments || []}
              timeRange="30d"
              selectedSocial={selectedSocial}
              height={chartHeight}
            />
          );
        case "Shares Chart":
          return (
            <SharesChart
              data={analyticsData?.shares || []}
              timeRange="30d"
              selectedSocial={selectedSocial}
              height={chartHeight}
            />
          );
        case "Saved Chart":
          return (
            <SavedChart
              data={analyticsData?.saves || []}
              timeRange="30d"
              selectedSocial={selectedSocial}
              height={chartHeight}
            />
          );
        case "Replies Chart":
          return (
            <RepliesChart
              data={analyticsData?.replies || []}
              timeRange="30d"
              selectedSocial={selectedSocial}
              height={chartHeight}
            />
          );
        case "Views Chart":
          return (
            <ViewsChart
              data={analyticsData?.views || []}
              timeRange="30d"
              selectedSocial={selectedSocial}
              height={chartHeight}
            />
          );
        case "Profile Links Taps":
          return (
            <ProfileLinksTapsChart
              data={analyticsData?.profile_links_taps || []}
              timeRange="30d"
              selectedSocial={selectedSocial}
              height={chartHeight}
            />
          );
        case "Follows and Unfollows":
          return (
            <FollowsAndUnfollowsChart
              data={analyticsData?.follows_and_unfollows || []}
              timeRange="30d"
              selectedSocial={selectedSocial}
              height={chartHeight}
            />
          );
        case "Engaged Audience Demographics":
          return (
            <EngagedAudienceDemographicsChart
              data={analyticsData?.engaged_audience_demographics || []}
              selectedSocial={selectedSocial}
              height={chartHeight}
            />
          );
        default:
          console.log("Unknown widget type:", widget.type);
          return <div>Content for {widget.type}</div>;
      }
    };

    return (
      <div
        className="relative w-full h-full p-1"
        style={{
          minHeight: `${widget.config.minH}px`,
          height: chartHeight,
        }}
      >
        {renderWidgetContent()}
      </div>
    );
  };

  const renderAddNewSection = () => {
    if (widgets.length >= MAX_WIDGETS) return null;
    return (
      <div
        onClick={() => setShowSelectWidgetModal(true)}
        className="col-span-1 border-2 border-dashed border-gray-300 rounded-lg
                  flex items-center justify-center cursor-pointer min-h-[200px]
                  hover:border-gray-400 hover:bg-gray-50 transition-all duration-200"
      >
        <div className="flex flex-col items-center text-gray-400 group-hover:text-gray-600">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-6 w-6 mb-2 bg-[#2C3E50] rounded-full p-1.5 text-white"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 4v16m8-8H4"
            />
          </svg>
          <span className="text-xs text-[#2C3E50]">Add Widget</span>
        </div>
      </div>
    );
  };

  const renderPaymentBanner = () => {
    if (!paymentStatus) return null;

    const modalConfig = {
      loading: {
        icon: (
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mb-4" />
        ),
        title: "Processing Payment",
        message: "Please wait while we verify your payment...",
        bgColor: "bg-white",
        textColor: "text-gray-800",
      },
      success: {
        icon: (
          <div className="rounded-full bg-green-100 p-2 mb-4">
            <svg
              className="h-8 w-8 text-green-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M5 13l4 4L19 7"
              />
            </svg>
          </div>
        ),
        title: "Payment Successful",
        message:
          "Thank you for your purchase! Your payment has been processed successfully.",
        bgColor: "bg-white",
        textColor: "text-gray-800",
      },
      error: {
        icon: (
          <div className="rounded-full bg-red-100 p-2 mb-4">
            <svg
              className="h-8 w-8 text-red-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </div>
        ),
        title: "Payment Failed",
        message:
          "There was an error processing your payment. Please try again or contact support.",
        bgColor: "bg-white",
        textColor: "text-gray-800",
      },
    }[paymentStatus];

    return (
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
        <div
          className={`${modalConfig.bgColor} rounded-lg shadow-xl max-w-md w-full mx-4 p-6`}
        >
          <div className="flex flex-col items-center text-center">
            {modalConfig.icon}
            <h3 className={`${modalConfig.textColor} text-lg  mb-2`}>
              {modalConfig.title}
            </h3>
            <p className={`${modalConfig.textColor} mb-4`}>
              {modalConfig.message}
            </p>
            <button
              onClick={() => {
                setPaymentStatus(null);
                router.push("/dashboard/user-profile");
              }}
              className="px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg text-gray-800 transition-colors"
            >
              Continue
            </button>
          </div>
        </div>
      </div>
    );
  };

  // if (isLoading) {
  //   return <LoadingScreen />;
  // }

  // CSS Grid configuration for responsive layout
  const getGridColumns = () => {
    return calculateGridColumns(screenWidth);
  };

  // Function to get responsive column span for widgets
  const getResponsiveColSpan = (widget: Widget) => {
    const baseColSpan = widget.config.colSpan;
    const gridColumns = getGridColumns();

    // Ensure widget doesn't exceed available columns
    const safeBase = Math.min(baseColSpan, gridColumns);

    // On smaller screens, make small widgets half-width (1 of 2), others full-width (2 of 2)
    if (screenWidth < 768) {
      const isSmall = widget.config.size === "small";
      return isSmall ? 1 : Math.min(2, gridColumns);
    }

    return safeBase;
  };

  // Compute which widgets fit within two rows based on current grid columns
  const getTwoRowLayout = (list: Widget[]) => {
    const cols = getGridColumns();
    let row = 1;
    let remaining = cols;
    const items: Widget[] = [];
    for (const w of list) {
      const span = Math.min(getResponsiveColSpan(w), cols);
      if (span <= remaining) {
        items.push(w);
        remaining -= span;
      } else {
        row += 1;
        if (row > 2) break;
        remaining = cols;
        if (span <= remaining) {
          items.push(w);
          remaining -= span;
        } else {
          // If a single widget exceeds row capacity, skip it
          continue;
        }
      }
    }
    return { items, rowsUsed: row, remainingCols: remaining, cols };
  };

  // Add useEffect to measure container height
  useEffect(() => {
    const updateHeight = () => {
      if (dashboardRef.current) {
        setContainerHeight(window.innerHeight);
      }
    };

    updateHeight();
    window.addEventListener("resize", updateHeight);
    return () => window.removeEventListener("resize", updateHeight);
  }, []);

  const checkIfWidgetFits = (widgetType: string) => {
    // With CSS Grid and gridAutoFlow: 'dense', widgets can automatically flow to new rows
    // So we don't need to restrict based on container height
    // Only check if the widget type is valid
    const config = widgetConfigs[widgetType as keyof typeof widgetConfigs];
    return !!config;
  };

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="flex min-h-screen">
        {renderPaymentBanner()}
        {/* Social Platform Sidebar (desktop only) */}
        <SocialAccountSelectionSidebar
          className="hidden md:flex"
          socialAccounts={
            (selectedWorkspaceDetails?.social_accounts ?? []).map(
              (a: SocialAccount) => ({
                ...a,
                username: a.username ?? "",
              })
            ) as any
          }
          selectedSocial={selectedSocial as any}
          onSocialSelect={(account) =>
            setUser({
              selectedSocial: {
                platform: account.platform,
                social_id: account.social_id,
                social_name: account.social_name,
                username: account.username ?? "",
              },
            })
          }
          onAddClick={() => setShowAddSocialModal(true)}
        />

        {/* Main Content */}
        <div className="flex-1 min-h-screen overflow-y-auto bg-gray-100">
          {!selectedSocial ? (
            <NoSocialSelected />
          ) : (
            <div
              ref={dashboardRef}
              className="p-3 sm:p-4 md:p-4 mt-[8vh] md:mt-0"
            >
              <div className="max-w-full">
                {/* Dashboard Header with Add Metrics Button */}
                <div className="flex justify-between items-center mb-6">
                  <h1 className="text-2xl font-bold text-gray-900">
                    Dashboard
                  </h1>
                </div>

                <div ref={masonryRef}>
                  {/* Dashboard Metrics Cards - first row */}
                  <div className="mb-4 w-full">
                    <div className="flex items-center justify-between mb-2">
                      <h2 className="text-lg md:text-xl font-semibold text-gray-900">
                        {timeRange === "30d"
                          ? "Past 30 days metrics"
                          : "Past 7 days metrics"}
                      </h2>
                      <div className="text-sm text-gray-500 flex items-center gap-3">
                        <button
                          onClick={() => setShowMetricsModal(true)}
                          className="flex items-center gap-2 px-3 py-2 bg-[#2c3e50] text-white rounded-md hover:bg-[#2c3e50] transition-colors text-xs"
                          disabled={isDashboardLoading}
                          aria-label="Add Metrics"
                        >
                          Add Metrics Cards
                          <i className="fas fa-plus" />
                        </button>
                      </div>
                    </div>
                    {isDashboardLoading ? (
                      <OverviewSkeleton />
                    ) : (
                      <div className="w-full max-w-full">
                        <div className="grid grid-cols-3 lg:grid-cols-6 gap-3 md:gap-4 w-full lg:px-3">
                          <AnimatePresence initial={false}>
                            {dashboardMetricsCards.map((metric, index) => {
                              const metricKey = selectedMetricsCards[index];
                              if (!metricKey) return null;
                              return (
                                <DashboardMetricCard
                                  key={metricKey}
                                  index={index}
                                  metric={metric}
                                  metricKey={metricKey}
                                />
                              );
                            })}
                          </AnimatePresence>
                        </div>
                      </div>
                    )}

                    {/* Widgets grid */}
                    {!analyticsData ? (
                      <WidgetsSkeleton />
                    ) : (
                      <div
                        className="dashboard-grid grid w-full mt-4"
                        style={{
                          gridTemplateColumns: `repeat(${getGridColumns()}, minmax(0, 1fr))`,
                          gridAutoRows:
                            screenWidth < 768
                              ? "minmax(350px, 350px)"
                              : "minmax(200px, 380px)",
                          // Use dense packing so items fill gaps on the same row
                          gridAutoFlow: "dense",
                          // Further reduce desktop gap for more horizontal room
                          gap: screenWidth < 768 ? "0.8rem" : "0.65rem",
                          display: "grid",
                          alignItems: "stretch",
                        }}
                      >
                        {widgets.map((widget: Widget, index: number) => (
                          <DraggableWidget
                            key={widget.id}
                            widget={widget}
                            index={index}
                            onRemove={handleRemoveWidget}
                            moveWidget={(dragIndex, hoverIndex) => {
                              const newWidgets = [...widgets];
                              const dragWidget = newWidgets[dragIndex];
                              if (dragWidget) {
                                newWidgets.splice(dragIndex, 1);
                                newWidgets.splice(hoverIndex, 0, dragWidget);
                                const updatedWidgets = newWidgets.map(
                                  (w, i) => ({ ...w, order: i })
                                );
                                setWidgets(updatedWidgets);
                              }
                            }}
                          >
                            {renderWidget(widget)}
                          </DraggableWidget>
                        ))}

                        {widgets.length < MAX_WIDGETS && (
                          <div
                            className="col-span-1 min-w-0"
                            style={{ gridColumn: `span 1` }}
                          >
                            {renderAddNewSection()}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {showSelectWidgetModal && (
          <SelectWidgetModal
            onClose={() => setShowSelectWidgetModal(false)}
            onSelect={handleAddWidget}
            availableWidgets={Object.keys(widgetConfigs).filter((key) =>
              ALLOWED_WIDGET_TYPES.includes(key)
            )}
            currentWidgets={widgets.map((w) => w.type)}
            maxReached={widgets.length >= MAX_WIDGETS}
            analyticsData={analyticsData}
            contentTypeData={contentTypeData}
            cityData={cityData}
            followersOverviewData={followersOverviewData}
            selectedSocial={selectedSocial as any}
            previewHeight={screenWidth < 768 ? "260px" : "380px"}
          />
        )}

        {showAddSocialModal && (
          <AddSocialAccountModal
            onClose={() => setShowAddSocialModal(false)}
            dim={true}
            onAddAccount={(platform) => {
              console.log(`Adding ${platform} account`);
              setShowAddSocialModal(false);
            }}
          />
        )}

        {/* Metrics Selection Modal */}
        <AnimatePresence>
          {showMetricsModal && (
            <AddMetricsModal
              isOpen={showMetricsModal}
              onClose={() => setShowMetricsModal(false)}
              availableMetrics={availableMetricsCards}
              onAddMetric={addMetricCard}
              maxReached={selectedMetricsCards.length >= 6}
              previewData={metricPreviewData}
              selectedMetrics={selectedMetricsCards}
              allMetrics={AVAILABLE_METRICS}
            />
          )}
        </AnimatePresence>
      </div>
    </DndProvider>
  );
};

export default Dashboard;
